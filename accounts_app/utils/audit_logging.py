# --- Standard Library Imports ---
import logging
import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
import traceback

# --- Django Imports ---
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.serializers.json import DjangoJSONEncoder
from django.utils import timezone
from django.db import transaction

# --- Local App Imports ---
from ..models.security import SecurityEvent
from .security_utils import get_client_ip

logger = logging.getLogger(__name__)
User = get_user_model()


class AuditLogger:
    """
    Comprehensive audit logging system.
    
    Features:
    - Structured audit logging
    - Security event tracking
    - User activity monitoring
    - System event logging
    - Compliance reporting
    """
    
    def __init__(self):
        self.audit_logger = logging.getLogger('audit')
        self.security_logger = logging.getLogger('security')
        self.performance_logger = logging.getLogger('performance')
        
        # Audit configuration
        self.enable_audit_logging = getattr(settings, 'ENABLE_AUDIT_LOGGING', True)
        self.enable_security_logging = getattr(settings, 'ENABLE_SECURITY_LOGGING', True)
        self.enable_performance_logging = getattr(settings, 'ENABLE_PERFORMANCE_LOGGING', True)
        
        # Retention settings
        self.audit_retention_days = getattr(settings, 'AUDIT_RETENTION_DAYS', 365)
        self.security_retention_days = getattr(settings, 'SECURITY_RETENTION_DAYS', 730)
        
        # Sensitive data fields to mask
        self.sensitive_fields = {
            'password', 'password1', 'password2', 'old_password', 'new_password',
            'current_password', 'token', 'secret', 'key', 'api_key', 'csrf_token',
            'csrfmiddlewaretoken', 'credit_card', 'ssn', 'social_security'
        }
    
    def log_authentication_event(self, event_type: str, user=None, request=None, 
                                success: bool = True, details: Dict = None):
        """
        Log authentication-related events.
        
        Args:
            event_type: Type of authentication event
            user: User object (optional)
            request: Django request object (optional)
            success: Whether the event was successful
            details: Additional event details
        """
        if not self.enable_audit_logging:
            return
        
        audit_data = self._build_audit_data(
            category='authentication',
            event_type=event_type,
            user=user,
            request=request,
            success=success,
            details=details
        )
        
        self._log_audit_event(audit_data)
        
        # Also log to security logger for failed authentication
        if not success:
            self._log_security_event(audit_data)
    
    def log_user_activity(self, activity_type: str, user=None, request=None,
                         target_object=None, details: Dict = None):
        """
        Log user activity events.
        
        Args:
            activity_type: Type of user activity
            user: User object
            request: Django request object (optional)
            target_object: Object being acted upon (optional)
            details: Additional activity details
        """
        if not self.enable_audit_logging:
            return
        
        audit_data = self._build_audit_data(
            category='user_activity',
            event_type=activity_type,
            user=user,
            request=request,
            target_object=target_object,
            details=details
        )
        
        self._log_audit_event(audit_data)
    
    def log_security_event(self, event_type: str, user=None, request=None,
                          severity: str = 'medium', details: Dict = None):
        """
        Log security-related events.
        
        Args:
            event_type: Type of security event
            user: User object (optional)
            request: Django request object (optional)
            severity: Event severity (low, medium, high, critical)
            details: Additional event details
        """
        if not self.enable_security_logging:
            return
        
        audit_data = self._build_audit_data(
            category='security',
            event_type=event_type,
            user=user,
            request=request,
            severity=severity,
            details=details
        )
        
        self._log_security_event(audit_data)
        
        # Store in database for security events
        self._store_security_event(audit_data)
    
    def log_system_event(self, event_type: str, component: str, details: Dict = None,
                        success: bool = True):
        """
        Log system-level events.
        
        Args:
            event_type: Type of system event
            component: System component generating the event
            details: Additional event details
            success: Whether the event was successful
        """
        if not self.enable_audit_logging:
            return
        
        audit_data = self._build_audit_data(
            category='system',
            event_type=event_type,
            component=component,
            success=success,
            details=details
        )
        
        self._log_audit_event(audit_data)
    
    def log_performance_event(self, event_type: str, duration: float, 
                            component: str = None, details: Dict = None):
        """
        Log performance-related events.
        
        Args:
            event_type: Type of performance event
            duration: Event duration in seconds
            component: Component being measured (optional)
            details: Additional performance details
        """
        if not self.enable_performance_logging:
            return
        
        performance_data = {
            'timestamp': timezone.now().isoformat(),
            'category': 'performance',
            'event_type': event_type,
            'duration': duration,
            'component': component,
            'details': details or {}
        }
        
        self.performance_logger.info(
            f"Performance event: {event_type}",
            extra={'audit_data': performance_data}
        )
    
    def log_data_access(self, user, resource_type: str, resource_id: str,
                       action: str, request=None, details: Dict = None):
        """
        Log data access events for compliance.
        
        Args:
            user: User accessing the data
            resource_type: Type of resource being accessed
            resource_id: ID of the resource
            action: Action being performed (read, create, update, delete)
            request: Django request object (optional)
            details: Additional access details
        """
        if not self.enable_audit_logging:
            return
        
        access_details = {
            'resource_type': resource_type,
            'resource_id': resource_id,
            'action': action,
            **(details or {})
        }
        
        audit_data = self._build_audit_data(
            category='data_access',
            event_type=f'{action}_{resource_type}',
            user=user,
            request=request,
            details=access_details
        )
        
        self._log_audit_event(audit_data)
    
    def log_admin_action(self, admin_user, action: str, target_user=None,
                        request=None, details: Dict = None):
        """
        Log administrative actions.
        
        Args:
            admin_user: User performing the admin action
            action: Administrative action being performed
            target_user: User being acted upon (optional)
            request: Django request object (optional)
            details: Additional action details
        """
        if not self.enable_audit_logging:
            return
        
        admin_details = {
            'admin_action': action,
            'target_user_id': target_user.id if target_user else None,
            'target_user_email': target_user.email if target_user else None,
            **(details or {})
        }
        
        audit_data = self._build_audit_data(
            category='admin_action',
            event_type=action,
            user=admin_user,
            request=request,
            details=admin_details
        )
        
        self._log_audit_event(audit_data)
        
        # Admin actions are also security events
        self._log_security_event(audit_data)
    
    def _build_audit_data(self, category: str, event_type: str, user=None,
                         request=None, target_object=None, component: str = None,
                         success: bool = True, severity: str = 'medium',
                         details: Dict = None) -> Dict:
        """Build comprehensive audit data structure."""
        audit_data = {
            'timestamp': timezone.now().isoformat(),
            'category': category,
            'event_type': event_type,
            'success': success,
            'severity': severity,
        }
        
        # User information
        if user:
            audit_data['user'] = {
                'id': user.id,
                'email': user.email,
                'username': getattr(user, 'username', None),
                'is_staff': getattr(user, 'is_staff', False),
                'is_superuser': getattr(user, 'is_superuser', False),
            }
        
        # Request information
        if request:
            audit_data['request'] = {
                'method': request.method,
                'path': request.path,
                'ip_address': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'referer': request.META.get('HTTP_REFERER', ''),
                'session_key': request.session.session_key if hasattr(request, 'session') else None,
            }
            
            # Add request data (sanitized)
            if request.method in ['POST', 'PUT', 'PATCH']:
                audit_data['request']['data'] = self._sanitize_request_data(request)
        
        # Target object information
        if target_object:
            audit_data['target_object'] = {
                'type': target_object.__class__.__name__,
                'id': getattr(target_object, 'id', None),
                'str_representation': str(target_object)[:100],
            }
        
        # Component information
        if component:
            audit_data['component'] = component
        
        # Additional details
        if details:
            audit_data['details'] = details
        
        return audit_data
    
    def _sanitize_request_data(self, request) -> Dict:
        """Sanitize request data to remove sensitive information."""
        sanitized_data = {}
        
        # Sanitize POST data
        if hasattr(request, 'POST'):
            for key, value in request.POST.items():
                if key.lower() in self.sensitive_fields:
                    sanitized_data[key] = '[REDACTED]'
                else:
                    sanitized_data[key] = str(value)[:200]  # Limit length
        
        return sanitized_data
    
    def _log_audit_event(self, audit_data: Dict):
        """Log audit event to audit logger."""
        try:
            self.audit_logger.info(
                f"Audit event: {audit_data['category']}.{audit_data['event_type']}",
                extra={'audit_data': audit_data}
            )
        except Exception as e:
            logger.error(f"Failed to log audit event: {str(e)}")
    
    def _log_security_event(self, audit_data: Dict):
        """Log security event to security logger."""
        try:
            self.security_logger.warning(
                f"Security event: {audit_data['category']}.{audit_data['event_type']}",
                extra={'audit_data': audit_data}
            )
        except Exception as e:
            logger.error(f"Failed to log security event: {str(e)}")
    
    def _store_security_event(self, audit_data: Dict):
        """Store security event in database."""
        try:
            with transaction.atomic():
                SecurityEvent.objects.create(
                    event_type=audit_data['event_type'],
                    category=audit_data['category'],
                    severity=audit_data.get('severity', 'medium'),
                    user_id=audit_data.get('user', {}).get('id'),
                    ip_address=audit_data.get('request', {}).get('ip_address'),
                    user_agent=audit_data.get('request', {}).get('user_agent', ''),
                    details=audit_data,
                    is_suspicious=audit_data.get('severity') in ['high', 'critical']
                )
        except Exception as e:
            logger.error(f"Failed to store security event: {str(e)}")


# Global audit logger instance
audit_logger = AuditLogger()


# Convenience functions
def log_authentication_event(event_type: str, user=None, request=None, 
                            success: bool = True, details: Dict = None):
    """Log authentication event."""
    audit_logger.log_authentication_event(event_type, user, request, success, details)


def log_user_activity(activity_type: str, user=None, request=None,
                     target_object=None, details: Dict = None):
    """Log user activity."""
    audit_logger.log_user_activity(activity_type, user, request, target_object, details)


def log_security_event(event_type: str, user=None, request=None,
                      severity: str = 'medium', details: Dict = None):
    """Log security event."""
    audit_logger.log_security_event(event_type, user, request, severity, details)


def log_system_event(event_type: str, component: str, details: Dict = None,
                    success: bool = True):
    """Log system event."""
    audit_logger.log_system_event(event_type, component, details, success)


def log_performance_event(event_type: str, duration: float, 
                         component: str = None, details: Dict = None):
    """Log performance event."""
    audit_logger.log_performance_event(event_type, duration, component, details)


def log_data_access(user, resource_type: str, resource_id: str,
                   action: str, request=None, details: Dict = None):
    """Log data access event."""
    audit_logger.log_data_access(user, resource_type, resource_id, action, request, details)


def log_admin_action(admin_user, action: str, target_user=None,
                    request=None, details: Dict = None):
    """Log admin action."""
    audit_logger.log_admin_action(admin_user, action, target_user, request, details)
