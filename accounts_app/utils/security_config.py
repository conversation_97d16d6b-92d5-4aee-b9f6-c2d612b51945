# --- Standard Library Imports ---
import os
import logging
from typing import Dict, List, Optional

# --- Django Imports ---
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

logger = logging.getLogger(__name__)


class SecurityConfigurationManager:
    """
    Comprehensive security configuration management.
    
    Features:
    - Security settings validation
    - Environment-specific configurations
    - Security recommendations
    - Configuration auditing
    """
    
    def __init__(self):
        self.environment = getattr(settings, 'ENVIRONMENT', 'development')
        self.debug = getattr(settings, 'DEBUG', True)
        
        # Security configuration categories
        self.categories = {
            'session': self._get_session_config,
            'csrf': self._get_csrf_config,
            'headers': self._get_headers_config,
            'https': self._get_https_config,
            'authentication': self._get_auth_config,
            'passwords': self._get_password_config,
            'rate_limiting': self._get_rate_limiting_config,
            'logging': self._get_logging_config,
        }
    
    def get_security_configuration(self) -> Dict:
        """Get comprehensive security configuration."""
        config = {}
        
        for category, config_func in self.categories.items():
            try:
                config[category] = config_func()
            except Exception as e:
                logger.error(f"Error getting {category} configuration: {str(e)}")
                config[category] = {'error': str(e)}
        
        return config
    
    def validate_security_configuration(self) -> Dict:
        """Validate security configuration and return recommendations."""
        validation_results = {
            'is_secure': True,
            'warnings': [],
            'errors': [],
            'recommendations': [],
            'score': 100,
        }
        
        # Validate each category
        for category, config_func in self.categories.items():
            category_validation = self._validate_category(category, config_func)
            
            if category_validation['errors']:
                validation_results['is_secure'] = False
                validation_results['errors'].extend(category_validation['errors'])
            
            validation_results['warnings'].extend(category_validation['warnings'])
            validation_results['recommendations'].extend(category_validation['recommendations'])
            validation_results['score'] -= category_validation['score_deduction']
        
        # Environment-specific validations
        if self.environment == 'production':
            prod_validation = self._validate_production_security()
            validation_results['warnings'].extend(prod_validation['warnings'])
            validation_results['errors'].extend(prod_validation['errors'])
            validation_results['score'] -= prod_validation['score_deduction']
        
        validation_results['score'] = max(0, validation_results['score'])
        
        return validation_results
    
    def _get_session_config(self) -> Dict:
        """Get session security configuration."""
        return {
            'SESSION_COOKIE_SECURE': getattr(settings, 'SESSION_COOKIE_SECURE', False),
            'SESSION_COOKIE_HTTPONLY': getattr(settings, 'SESSION_COOKIE_HTTPONLY', True),
            'SESSION_COOKIE_SAMESITE': getattr(settings, 'SESSION_COOKIE_SAMESITE', 'Lax'),
            'SESSION_COOKIE_AGE': getattr(settings, 'SESSION_COOKIE_AGE', 1209600),
            'SESSION_EXPIRE_AT_BROWSER_CLOSE': getattr(settings, 'SESSION_EXPIRE_AT_BROWSER_CLOSE', False),
            'SESSION_SAVE_EVERY_REQUEST': getattr(settings, 'SESSION_SAVE_EVERY_REQUEST', False),
        }
    
    def _get_csrf_config(self) -> Dict:
        """Get CSRF protection configuration."""
        return {
            'CSRF_COOKIE_SECURE': getattr(settings, 'CSRF_COOKIE_SECURE', False),
            'CSRF_COOKIE_HTTPONLY': getattr(settings, 'CSRF_COOKIE_HTTPONLY', True),
            'CSRF_COOKIE_SAMESITE': getattr(settings, 'CSRF_COOKIE_SAMESITE', 'Lax'),
            'CSRF_COOKIE_AGE': getattr(settings, 'CSRF_COOKIE_AGE', 31449600),
            'CSRF_USE_SESSIONS': getattr(settings, 'CSRF_USE_SESSIONS', False),
            'CSRF_TRUSTED_ORIGINS': getattr(settings, 'CSRF_TRUSTED_ORIGINS', []),
        }
    
    def _get_headers_config(self) -> Dict:
        """Get security headers configuration."""
        return {
            'SECURITY_ENABLE_HSTS': getattr(settings, 'SECURITY_ENABLE_HSTS', True),
            'SECURITY_HSTS_MAX_AGE': getattr(settings, 'SECURITY_HSTS_MAX_AGE', 31536000),
            'SECURITY_HSTS_INCLUDE_SUBDOMAINS': getattr(settings, 'SECURITY_HSTS_INCLUDE_SUBDOMAINS', True),
            'SECURITY_HSTS_PRELOAD': getattr(settings, 'SECURITY_HSTS_PRELOAD', True),
            'SECURITY_ENABLE_CSP': getattr(settings, 'SECURITY_ENABLE_CSP', True),
            'SECURITY_CSP_REPORT_ONLY': getattr(settings, 'SECURITY_CSP_REPORT_ONLY', False),
            'SECURITY_FRAME_OPTIONS': getattr(settings, 'SECURITY_FRAME_OPTIONS', 'DENY'),
            'SECURITY_CONTENT_TYPE_OPTIONS': getattr(settings, 'SECURITY_CONTENT_TYPE_OPTIONS', 'nosniff'),
            'SECURITY_XSS_PROTECTION': getattr(settings, 'SECURITY_XSS_PROTECTION', '1; mode=block'),
            'SECURITY_REFERRER_POLICY': getattr(settings, 'SECURITY_REFERRER_POLICY', 'strict-origin-when-cross-origin'),
        }
    
    def _get_https_config(self) -> Dict:
        """Get HTTPS configuration."""
        return {
            'SECURE_SSL_REDIRECT': getattr(settings, 'SECURE_SSL_REDIRECT', False),
            'SECURE_PROXY_SSL_HEADER': getattr(settings, 'SECURE_PROXY_SSL_HEADER', None),
            'USE_TLS': getattr(settings, 'USE_TLS', False),
            'ALLOWED_HOSTS': getattr(settings, 'ALLOWED_HOSTS', []),
        }
    
    def _get_auth_config(self) -> Dict:
        """Get authentication configuration."""
        return {
            'ACCOUNT_EMAIL_VERIFICATION': getattr(settings, 'ACCOUNT_EMAIL_VERIFICATION', 'optional'),
            'ACCOUNT_EMAIL_REQUIRED': getattr(settings, 'ACCOUNT_EMAIL_REQUIRED', False),
            'ACCOUNT_USERNAME_REQUIRED': getattr(settings, 'ACCOUNT_USERNAME_REQUIRED', True),
            'ACCOUNT_AUTHENTICATION_METHOD': getattr(settings, 'ACCOUNT_AUTHENTICATION_METHOD', 'username'),
            'ACCOUNT_RATE_LIMITS': getattr(settings, 'ACCOUNT_RATE_LIMITS', {}),
            'MAX_CONCURRENT_SESSIONS': getattr(settings, 'MAX_CONCURRENT_SESSIONS', 5),
        }
    
    def _get_password_config(self) -> Dict:
        """Get password security configuration."""
        return {
            'PASSWORD_MIN_LENGTH': getattr(settings, 'PASSWORD_MIN_LENGTH', 8),
            'PASSWORD_MAX_LENGTH': getattr(settings, 'PASSWORD_MAX_LENGTH', 128),
            'PASSWORD_REQUIRE_UPPERCASE': getattr(settings, 'PASSWORD_REQUIRE_UPPERCASE', True),
            'PASSWORD_REQUIRE_LOWERCASE': getattr(settings, 'PASSWORD_REQUIRE_LOWERCASE', True),
            'PASSWORD_REQUIRE_NUMBERS': getattr(settings, 'PASSWORD_REQUIRE_NUMBERS', True),
            'PASSWORD_REQUIRE_SYMBOLS': getattr(settings, 'PASSWORD_REQUIRE_SYMBOLS', True),
            'PASSWORD_HISTORY_COUNT': getattr(settings, 'PASSWORD_HISTORY_COUNT', 5),
            'PASSWORD_ENABLE_BREACH_CHECK': getattr(settings, 'PASSWORD_ENABLE_BREACH_CHECK', True),
        }
    
    def _get_rate_limiting_config(self) -> Dict:
        """Get rate limiting configuration."""
        return {
            'RATELIMIT_ENABLE': getattr(settings, 'RATELIMIT_ENABLE', True),
            'RATE_LIMIT_DEFAULTS': getattr(settings, 'RATE_LIMIT_DEFAULTS', {}),
            'PROGRESSIVE_RATE_LIMITING': getattr(settings, 'PROGRESSIVE_RATE_LIMITING', {}),
            'ACCOUNT_MAX_FAILED_ATTEMPTS': getattr(settings, 'ACCOUNT_MAX_FAILED_ATTEMPTS', 5),
            'ACCOUNT_LOCKOUT_DURATION': getattr(settings, 'ACCOUNT_LOCKOUT_DURATION', 1800),
        }
    
    def _get_logging_config(self) -> Dict:
        """Get security logging configuration."""
        return {
            'SECURITY_LOG_FAILED_LOGINS': getattr(settings, 'SECURITY_LOG_FAILED_LOGINS', True),
            'SECURITY_LOG_SUSPICIOUS_ACTIVITY': getattr(settings, 'SECURITY_LOG_SUSPICIOUS_ACTIVITY', True),
            'SECURITY_ALERT_EMAIL': getattr(settings, 'SECURITY_ALERT_EMAIL', None),
            'ENABLE_SECURITY_NOTIFICATIONS': getattr(settings, 'ENABLE_SECURITY_NOTIFICATIONS', True),
        }
    
    def _validate_category(self, category: str, config_func) -> Dict:
        """Validate a specific configuration category."""
        validation = {
            'errors': [],
            'warnings': [],
            'recommendations': [],
            'score_deduction': 0,
        }
        
        try:
            config = config_func()
            
            # Category-specific validations
            if category == 'session':
                validation.update(self._validate_session_config(config))
            elif category == 'csrf':
                validation.update(self._validate_csrf_config(config))
            elif category == 'headers':
                validation.update(self._validate_headers_config(config))
            elif category == 'https':
                validation.update(self._validate_https_config(config))
            elif category == 'authentication':
                validation.update(self._validate_auth_config(config))
            elif category == 'passwords':
                validation.update(self._validate_password_config(config))
            elif category == 'rate_limiting':
                validation.update(self._validate_rate_limiting_config(config))
            elif category == 'logging':
                validation.update(self._validate_logging_config(config))
        
        except Exception as e:
            validation['errors'].append(f"Error validating {category}: {str(e)}")
            validation['score_deduction'] = 20
        
        return validation
    
    def _validate_session_config(self, config: Dict) -> Dict:
        """Validate session configuration."""
        validation = {'errors': [], 'warnings': [], 'recommendations': [], 'score_deduction': 0}
        
        if not config.get('SESSION_COOKIE_SECURE') and self.environment == 'production':
            validation['errors'].append('SESSION_COOKIE_SECURE should be True in production')
            validation['score_deduction'] += 15
        
        if not config.get('SESSION_COOKIE_HTTPONLY'):
            validation['errors'].append('SESSION_COOKIE_HTTPONLY should be True')
            validation['score_deduction'] += 10
        
        if config.get('SESSION_COOKIE_SAMESITE') not in ['Lax', 'Strict']:
            validation['warnings'].append('SESSION_COOKIE_SAMESITE should be Lax or Strict')
            validation['score_deduction'] += 5
        
        if config.get('SESSION_COOKIE_AGE', 0) > 86400:  # 24 hours
            validation['recommendations'].append('Consider shorter session timeout for better security')
        
        return validation
    
    def _validate_csrf_config(self, config: Dict) -> Dict:
        """Validate CSRF configuration."""
        validation = {'errors': [], 'warnings': [], 'recommendations': [], 'score_deduction': 0}
        
        if not config.get('CSRF_COOKIE_SECURE') and self.environment == 'production':
            validation['errors'].append('CSRF_COOKIE_SECURE should be True in production')
            validation['score_deduction'] += 15
        
        if not config.get('CSRF_COOKIE_HTTPONLY'):
            validation['warnings'].append('CSRF_COOKIE_HTTPONLY should be True for enhanced security')
            validation['score_deduction'] += 5
        
        return validation
    
    def _validate_headers_config(self, config: Dict) -> Dict:
        """Validate security headers configuration."""
        validation = {'errors': [], 'warnings': [], 'recommendations': [], 'score_deduction': 0}
        
        if not config.get('SECURITY_ENABLE_HSTS') and self.environment == 'production':
            validation['warnings'].append('HSTS should be enabled in production')
            validation['score_deduction'] += 10
        
        if not config.get('SECURITY_ENABLE_CSP'):
            validation['warnings'].append('Content Security Policy should be enabled')
            validation['score_deduction'] += 10
        
        if config.get('SECURITY_FRAME_OPTIONS') not in ['DENY', 'SAMEORIGIN']:
            validation['warnings'].append('X-Frame-Options should be DENY or SAMEORIGIN')
            validation['score_deduction'] += 5
        
        return validation
    
    def _validate_https_config(self, config: Dict) -> Dict:
        """Validate HTTPS configuration."""
        validation = {'errors': [], 'warnings': [], 'recommendations': [], 'score_deduction': 0}
        
        if not config.get('SECURE_SSL_REDIRECT') and self.environment == 'production':
            validation['errors'].append('SECURE_SSL_REDIRECT should be True in production')
            validation['score_deduction'] += 20
        
        if not config.get('ALLOWED_HOSTS') or '*' in config.get('ALLOWED_HOSTS', []):
            validation['errors'].append('ALLOWED_HOSTS should be properly configured')
            validation['score_deduction'] += 15
        
        return validation
    
    def _validate_auth_config(self, config: Dict) -> Dict:
        """Validate authentication configuration."""
        validation = {'errors': [], 'warnings': [], 'recommendations': [], 'score_deduction': 0}
        
        if config.get('ACCOUNT_EMAIL_VERIFICATION') != 'mandatory':
            validation['warnings'].append('Email verification should be mandatory')
            validation['score_deduction'] += 10
        
        if not config.get('ACCOUNT_EMAIL_REQUIRED'):
            validation['warnings'].append('Email should be required for accounts')
            validation['score_deduction'] += 5
        
        return validation
    
    def _validate_password_config(self, config: Dict) -> Dict:
        """Validate password configuration."""
        validation = {'errors': [], 'warnings': [], 'recommendations': [], 'score_deduction': 0}
        
        if config.get('PASSWORD_MIN_LENGTH', 0) < 8:
            validation['errors'].append('Minimum password length should be at least 8')
            validation['score_deduction'] += 15
        
        required_checks = ['PASSWORD_REQUIRE_UPPERCASE', 'PASSWORD_REQUIRE_LOWERCASE', 
                          'PASSWORD_REQUIRE_NUMBERS', 'PASSWORD_REQUIRE_SYMBOLS']
        
        for check in required_checks:
            if not config.get(check):
                validation['warnings'].append(f'{check} should be enabled')
                validation['score_deduction'] += 3
        
        if not config.get('PASSWORD_ENABLE_BREACH_CHECK'):
            validation['recommendations'].append('Enable password breach checking')
        
        return validation
    
    def _validate_rate_limiting_config(self, config: Dict) -> Dict:
        """Validate rate limiting configuration."""
        validation = {'errors': [], 'warnings': [], 'recommendations': [], 'score_deduction': 0}
        
        if not config.get('RATELIMIT_ENABLE'):
            validation['errors'].append('Rate limiting should be enabled')
            validation['score_deduction'] += 20
        
        if config.get('ACCOUNT_MAX_FAILED_ATTEMPTS', 0) > 10:
            validation['warnings'].append('Maximum failed attempts should be 10 or less')
            validation['score_deduction'] += 5
        
        return validation
    
    def _validate_logging_config(self, config: Dict) -> Dict:
        """Validate logging configuration."""
        validation = {'errors': [], 'warnings': [], 'recommendations': [], 'score_deduction': 0}
        
        if not config.get('SECURITY_LOG_FAILED_LOGINS'):
            validation['warnings'].append('Failed login logging should be enabled')
            validation['score_deduction'] += 5
        
        if not config.get('SECURITY_LOG_SUSPICIOUS_ACTIVITY'):
            validation['warnings'].append('Suspicious activity logging should be enabled')
            validation['score_deduction'] += 5
        
        return validation
    
    def _validate_production_security(self) -> Dict:
        """Validate production-specific security requirements."""
        validation = {'errors': [], 'warnings': [], 'score_deduction': 0}
        
        if self.debug:
            validation['errors'].append('DEBUG should be False in production')
            validation['score_deduction'] += 25
        
        if not os.environ.get('SECRET_KEY'):
            validation['errors'].append('SECRET_KEY should be set via environment variable')
            validation['score_deduction'] += 20
        
        return validation


# Global security configuration manager
security_config = SecurityConfigurationManager()


def get_security_configuration() -> Dict:
    """Get comprehensive security configuration."""
    return security_config.get_security_configuration()


def validate_security_configuration() -> Dict:
    """Validate security configuration."""
    return security_config.validate_security_configuration()
