# --- Standard Library Imports ---
import re
import time
import random
import string
from typing import Dict, List, Optional, Tuple
from unittest.mock import Mock

# --- Django Imports ---
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.conf import settings
from django.core.cache import cache

# --- Local App Imports ---
from .password_security import validate_password
from .security_utils import check_rate_limit, get_client_ip
from .account_security import check_account_lockout, detect_suspicious_activity

User = get_user_model()


class SecurityTestingUtilities:
    """
    Comprehensive security testing utilities.
    
    Features:
    - Password strength testing
    - Rate limiting testing
    - XSS/SQL injection testing
    - Authentication security testing
    - Session security testing
    """
    
    def __init__(self):
        self.client = Client()
        
        # Test payloads for security testing
        self.xss_payloads = [
            '<script>alert("XSS")</script>',
            'javascript:alert("XSS")',
            '<img src="x" onerror="alert(\'XSS\')">',
            '<svg onload="alert(\'XSS\')">',
            '"><script>alert("XSS")</script>',
            "';alert('XSS');//",
            '<iframe src="javascript:alert(\'XSS\')"></iframe>',
        ]
        
        self.sql_injection_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "admin'/*",
            "' OR 1=1#",
            "' OR 'a'='a",
            "') OR ('1'='1",
        ]
        
        self.common_passwords = [
            'password', '123456', 'password123', 'admin', 'qwerty',
            'letmein', 'welcome', 'monkey', 'dragon', 'master'
        ]
    
    def test_password_strength(self, passwords: List[str] = None) -> Dict:
        """
        Test password strength validation.
        
        Args:
            passwords: List of passwords to test (optional)
            
        Returns:
            Dict with test results
        """
        test_passwords = passwords or [
            'weak',
            'password123',
            'StrongP@ssw0rd!',
            'VeryStr0ng!P@ssw0rd2024',
            '12345678',
            'ALLUPPERCASE',
            'alllowercase',
            'NoNumbers!',
            'NoSymbols123',
        ]
        
        results = {
            'total_tested': len(test_passwords),
            'passed': 0,
            'failed': 0,
            'details': []
        }
        
        for password in test_passwords:
            validation_result = validate_password(password)
            
            test_result = {
                'password': password,
                'is_valid': validation_result['is_valid'],
                'score': validation_result['score'],
                'strength_level': validation_result['strength_level'],
                'errors': validation_result['errors'],
                'warnings': validation_result['warnings'],
            }
            
            if validation_result['is_valid']:
                results['passed'] += 1
            else:
                results['failed'] += 1
            
            results['details'].append(test_result)
        
        return results
    
    def test_rate_limiting(self, endpoint: str, max_attempts: int = 10) -> Dict:
        """
        Test rate limiting on an endpoint.
        
        Args:
            endpoint: URL endpoint to test
            max_attempts: Maximum attempts to make
            
        Returns:
            Dict with test results
        """
        results = {
            'endpoint': endpoint,
            'attempts_made': 0,
            'rate_limited': False,
            'rate_limit_triggered_at': None,
            'responses': []
        }
        
        for attempt in range(max_attempts):
            try:
                response = self.client.post(endpoint, {
                    'email': '<EMAIL>',
                    'password': 'wrongpassword'
                })
                
                results['attempts_made'] += 1
                results['responses'].append({
                    'attempt': attempt + 1,
                    'status_code': response.status_code,
                    'rate_limited': response.status_code == 429
                })
                
                if response.status_code == 429:
                    results['rate_limited'] = True
                    results['rate_limit_triggered_at'] = attempt + 1
                    break
                
                # Small delay between attempts
                time.sleep(0.1)
                
            except Exception as e:
                results['responses'].append({
                    'attempt': attempt + 1,
                    'error': str(e)
                })
        
        return results
    
    def test_xss_protection(self, form_fields: Dict[str, str]) -> Dict:
        """
        Test XSS protection on form fields.
        
        Args:
            form_fields: Dict of field names and endpoints
            
        Returns:
            Dict with test results
        """
        results = {
            'total_payloads': len(self.xss_payloads),
            'total_fields': len(form_fields),
            'vulnerabilities_found': 0,
            'details': []
        }
        
        for field_name, endpoint in form_fields.items():
            field_results = {
                'field_name': field_name,
                'endpoint': endpoint,
                'vulnerable_payloads': [],
                'safe_payloads': []
            }
            
            for payload in self.xss_payloads:
                try:
                    response = self.client.post(endpoint, {field_name: payload})
                    
                    # Check if payload appears unescaped in response
                    if payload in response.content.decode('utf-8', errors='ignore'):
                        field_results['vulnerable_payloads'].append(payload)
                        results['vulnerabilities_found'] += 1
                    else:
                        field_results['safe_payloads'].append(payload)
                
                except Exception as e:
                    field_results['safe_payloads'].append(f"{payload} (Error: {str(e)})")
            
            results['details'].append(field_results)
        
        return results
    
    def test_sql_injection_protection(self, form_fields: Dict[str, str]) -> Dict:
        """
        Test SQL injection protection on form fields.
        
        Args:
            form_fields: Dict of field names and endpoints
            
        Returns:
            Dict with test results
        """
        results = {
            'total_payloads': len(self.sql_injection_payloads),
            'total_fields': len(form_fields),
            'vulnerabilities_found': 0,
            'details': []
        }
        
        for field_name, endpoint in form_fields.items():
            field_results = {
                'field_name': field_name,
                'endpoint': endpoint,
                'suspicious_responses': [],
                'safe_responses': []
            }
            
            for payload in self.sql_injection_payloads:
                try:
                    response = self.client.post(endpoint, {field_name: payload})
                    
                    # Check for SQL error messages or suspicious behavior
                    response_text = response.content.decode('utf-8', errors='ignore').lower()
                    sql_error_indicators = [
                        'sql syntax', 'mysql_fetch', 'ora-', 'microsoft odbc',
                        'sqlite_', 'postgresql', 'warning: mysql', 'error in your sql'
                    ]
                    
                    if any(indicator in response_text for indicator in sql_error_indicators):
                        field_results['suspicious_responses'].append({
                            'payload': payload,
                            'status_code': response.status_code,
                            'indicators_found': [ind for ind in sql_error_indicators if ind in response_text]
                        })
                        results['vulnerabilities_found'] += 1
                    else:
                        field_results['safe_responses'].append(payload)
                
                except Exception as e:
                    field_results['safe_responses'].append(f"{payload} (Error: {str(e)})")
            
            results['details'].append(field_results)
        
        return results
    
    def test_session_security(self, user_credentials: Dict[str, str]) -> Dict:
        """
        Test session security mechanisms.
        
        Args:
            user_credentials: Dict with 'email' and 'password'
            
        Returns:
            Dict with test results
        """
        results = {
            'session_fixation_protected': False,
            'session_regenerated_on_login': False,
            'session_expires_properly': False,
            'concurrent_session_handling': False,
            'details': []
        }
        
        # Test session fixation protection
        try:
            # Get initial session
            self.client.get('/accounts/login/')
            initial_session = self.client.session.session_key
            
            # Login
            login_response = self.client.post('/accounts/login/', user_credentials)
            post_login_session = self.client.session.session_key
            
            # Check if session was regenerated
            if initial_session != post_login_session:
                results['session_fixation_protected'] = True
                results['session_regenerated_on_login'] = True
            
            results['details'].append({
                'test': 'session_fixation',
                'initial_session': initial_session,
                'post_login_session': post_login_session,
                'regenerated': initial_session != post_login_session
            })
        
        except Exception as e:
            results['details'].append({
                'test': 'session_fixation',
                'error': str(e)
            })
        
        # Test concurrent session handling
        try:
            # Create second client
            client2 = Client()
            
            # Login with both clients
            self.client.post('/accounts/login/', user_credentials)
            client2.post('/accounts/login/', user_credentials)
            
            # Check if first session is still valid
            response1 = self.client.get('/dashboard/')
            response2 = client2.get('/dashboard/')
            
            results['details'].append({
                'test': 'concurrent_sessions',
                'client1_status': response1.status_code,
                'client2_status': response2.status_code,
                'both_active': response1.status_code == 200 and response2.status_code == 200
            })
        
        except Exception as e:
            results['details'].append({
                'test': 'concurrent_sessions',
                'error': str(e)
            })
        
        return results
    
    def test_authentication_security(self, test_user_email: str = '<EMAIL>') -> Dict:
        """
        Test authentication security mechanisms.
        
        Args:
            test_user_email: Email for test user
            
        Returns:
            Dict with test results
        """
        results = {
            'account_lockout_working': False,
            'suspicious_activity_detected': False,
            'password_requirements_enforced': False,
            'details': []
        }
        
        # Test account lockout
        try:
            lockout_attempts = 6  # Should exceed the limit
            for attempt in range(lockout_attempts):
                response = self.client.post('/accounts/login/', {
                    'email': test_user_email,
                    'password': 'wrongpassword'
                })
                
                if response.status_code == 429 or 'locked' in response.content.decode().lower():
                    results['account_lockout_working'] = True
                    results['details'].append({
                        'test': 'account_lockout',
                        'triggered_at_attempt': attempt + 1
                    })
                    break
        
        except Exception as e:
            results['details'].append({
                'test': 'account_lockout',
                'error': str(e)
            })
        
        # Test password requirements
        try:
            weak_passwords = ['123', 'password', 'abc']
            for weak_password in weak_passwords:
                validation_result = validate_password(weak_password)
                if not validation_result['is_valid']:
                    results['password_requirements_enforced'] = True
                    break
            
            results['details'].append({
                'test': 'password_requirements',
                'enforced': results['password_requirements_enforced']
            })
        
        except Exception as e:
            results['details'].append({
                'test': 'password_requirements',
                'error': str(e)
            })
        
        return results
    
    def generate_security_report(self) -> Dict:
        """
        Generate comprehensive security test report.
        
        Returns:
            Dict with complete security assessment
        """
        report = {
            'timestamp': time.time(),
            'tests_performed': [],
            'overall_score': 0,
            'recommendations': [],
            'critical_issues': [],
            'warnings': []
        }
        
        # Test password strength
        password_results = self.test_password_strength()
        report['tests_performed'].append({
            'test_name': 'password_strength',
            'results': password_results
        })
        
        # Test rate limiting on login
        try:
            rate_limit_results = self.test_rate_limiting('/accounts/login/')
            report['tests_performed'].append({
                'test_name': 'rate_limiting',
                'results': rate_limit_results
            })
        except Exception as e:
            report['warnings'].append(f"Rate limiting test failed: {str(e)}")
        
        # Test XSS protection on common fields
        xss_fields = {
            'email': '/accounts/login/',
            'password': '/accounts/login/',
        }
        xss_results = self.test_xss_protection(xss_fields)
        report['tests_performed'].append({
            'test_name': 'xss_protection',
            'results': xss_results
        })
        
        # Calculate overall score
        total_tests = len(report['tests_performed'])
        passed_tests = 0
        
        for test in report['tests_performed']:
            if test['test_name'] == 'password_strength':
                if test['results']['passed'] > test['results']['failed']:
                    passed_tests += 1
            elif test['test_name'] == 'rate_limiting':
                if test['results']['rate_limited']:
                    passed_tests += 1
            elif test['test_name'] == 'xss_protection':
                if test['results']['vulnerabilities_found'] == 0:
                    passed_tests += 1
        
        report['overall_score'] = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        # Generate recommendations
        if report['overall_score'] < 80:
            report['recommendations'].append('Improve overall security posture')
        if xss_results['vulnerabilities_found'] > 0:
            report['critical_issues'].append('XSS vulnerabilities detected')
        
        return report


# Global security testing utilities instance
security_testing = SecurityTestingUtilities()


# Convenience functions
def test_password_strength(passwords: List[str] = None) -> Dict:
    """Test password strength validation."""
    return security_testing.test_password_strength(passwords)


def test_rate_limiting(endpoint: str, max_attempts: int = 10) -> Dict:
    """Test rate limiting on endpoint."""
    return security_testing.test_rate_limiting(endpoint, max_attempts)


def test_xss_protection(form_fields: Dict[str, str]) -> Dict:
    """Test XSS protection."""
    return security_testing.test_xss_protection(form_fields)


def test_sql_injection_protection(form_fields: Dict[str, str]) -> Dict:
    """Test SQL injection protection."""
    return security_testing.test_sql_injection_protection(form_fields)


def generate_security_report() -> Dict:
    """Generate comprehensive security report."""
    return security_testing.generate_security_report()
