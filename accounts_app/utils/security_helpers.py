# --- Standard Library Imports ---
import re
import hashlib
import secrets
import base64
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

# --- Django Imports ---
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.html import escape
from django.utils.safestring import mark_safe
from django.contrib.auth.tokens import default_token_generator
from django.contrib.auth import get_user_model

User = get_user_model()


class SecurityValidationHelpers:
    """
    Collection of security validation helper functions.
    
    Features:
    - Input validation helpers
    - Token generation and validation
    - Secure comparison utilities
    - Security header helpers
    - Cryptographic utilities
    """
    
    def __init__(self):
        # Security patterns
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        self.phone_pattern = re.compile(r'^\+?[1-9]\d{1,14}$')
        self.url_pattern = re.compile(r'^https?://[^\s/$.?#].[^\s]*$')
        
        # Dangerous patterns
        self.xss_patterns = [
            re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
            re.compile(r'javascript:', re.IGNORECASE),
            re.compile(r'on\w+\s*=', re.IGNORECASE),
            re.compile(r'<iframe[^>]*>.*?</iframe>', re.IGNORECASE | re.DOTALL),
        ]
        
        self.sql_patterns = [
            re.compile(r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)', re.IGNORECASE),
            re.compile(r'(\b(OR|AND)\s+\d+\s*=\s*\d+)', re.IGNORECASE),
            re.compile(r'(--|\#|\/\*|\*\/)', re.IGNORECASE),
        ]
    
    def validate_email_security(self, email: str) -> Dict[str, Any]:
        """
        Comprehensive email validation with security checks.
        
        Args:
            email: Email address to validate
            
        Returns:
            Dict with validation results
        """
        result = {
            'is_valid': False,
            'is_secure': False,
            'issues': [],
            'warnings': [],
            'normalized_email': None
        }
        
        if not email:
            result['issues'].append('Email is required')
            return result
        
        # Normalize email
        email = email.strip().lower()
        result['normalized_email'] = email
        
        # Basic format validation
        if not self.email_pattern.match(email):
            result['issues'].append('Invalid email format')
            return result
        
        result['is_valid'] = True
        
        # Security checks
        domain = email.split('@')[1]
        
        # Check for suspicious domains
        suspicious_domains = [
            'tempmail.org', '10minutemail.com', 'guerrillamail.com',
            'mailinator.com', 'throwaway.email'
        ]
        
        if domain in suspicious_domains:
            result['warnings'].append('Temporary email domain detected')
        
        # Check for plus addressing (<EMAIL>)
        if '+' in email.split('@')[0]:
            result['warnings'].append('Plus addressing detected')
        
        # Check for suspicious patterns
        if self.contains_xss_patterns(email):
            result['issues'].append('Email contains suspicious patterns')
            result['is_secure'] = False
            return result
        
        result['is_secure'] = True
        return result
    
    def validate_url_security(self, url: str) -> Dict[str, Any]:
        """
        Validate URL with security checks.
        
        Args:
            url: URL to validate
            
        Returns:
            Dict with validation results
        """
        result = {
            'is_valid': False,
            'is_secure': False,
            'issues': [],
            'warnings': [],
            'parsed_url': None
        }
        
        if not url:
            result['issues'].append('URL is required')
            return result
        
        # Basic format validation
        if not self.url_pattern.match(url):
            result['issues'].append('Invalid URL format')
            return result
        
        result['is_valid'] = True
        
        try:
            parsed = urlparse(url)
            result['parsed_url'] = {
                'scheme': parsed.scheme,
                'netloc': parsed.netloc,
                'path': parsed.path,
                'params': parsed.params,
                'query': parsed.query,
                'fragment': parsed.fragment
            }
            
            # Security checks
            if parsed.scheme not in ['http', 'https']:
                result['issues'].append('Only HTTP/HTTPS URLs are allowed')
                return result
            
            if parsed.scheme == 'http':
                result['warnings'].append('HTTP URL is not secure, consider HTTPS')
            
            # Check for suspicious domains
            suspicious_tlds = ['.tk', '.ml', '.ga', '.cf']
            if any(parsed.netloc.endswith(tld) for tld in suspicious_tlds):
                result['warnings'].append('Suspicious top-level domain')
            
            # Check for IP addresses
            ip_pattern = re.compile(r'^\d+\.\d+\.\d+\.\d+$')
            if ip_pattern.match(parsed.netloc):
                result['warnings'].append('IP address instead of domain name')
            
            result['is_secure'] = True
            
        except Exception as e:
            result['issues'].append(f'URL parsing error: {str(e)}')
        
        return result
    
    def contains_xss_patterns(self, text: str) -> bool:
        """
        Check if text contains XSS patterns.
        
        Args:
            text: Text to check
            
        Returns:
            True if XSS patterns found
        """
        if not text:
            return False
        
        text_lower = text.lower()
        
        for pattern in self.xss_patterns:
            if pattern.search(text_lower):
                return True
        
        return False
    
    def contains_sql_patterns(self, text: str) -> bool:
        """
        Check if text contains SQL injection patterns.
        
        Args:
            text: Text to check
            
        Returns:
            True if SQL patterns found
        """
        if not text:
            return False
        
        for pattern in self.sql_patterns:
            if pattern.search(text):
                return True
        
        return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """
        Generate cryptographically secure token.
        
        Args:
            length: Token length in bytes
            
        Returns:
            Secure token string
        """
        return secrets.token_urlsafe(length)
    
    def generate_secure_password(self, length: int = 16) -> str:
        """
        Generate secure password.
        
        Args:
            length: Password length
            
        Returns:
            Secure password string
        """
        if length < 8:
            length = 8
        
        # Character sets
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?'
        
        # Ensure at least one character from each set
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(symbols)
        ]
        
        # Fill remaining length
        all_chars = lowercase + uppercase + digits + symbols
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))
        
        # Shuffle the password
        secrets.SystemRandom().shuffle(password)
        
        return ''.join(password)
    
    def secure_compare(self, a: str, b: str) -> bool:
        """
        Timing-safe string comparison.
        
        Args:
            a: First string
            b: Second string
            
        Returns:
            True if strings are equal
        """
        return secrets.compare_digest(a.encode('utf-8'), b.encode('utf-8'))
    
    def hash_sensitive_data(self, data: str, salt: str = None) -> Dict[str, str]:
        """
        Hash sensitive data with salt.
        
        Args:
            data: Data to hash
            salt: Optional salt (generated if not provided)
            
        Returns:
            Dict with hash and salt
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        # Combine data and salt
        salted_data = f"{data}{salt}"
        
        # Create hash
        hash_value = hashlib.sha256(salted_data.encode('utf-8')).hexdigest()
        
        return {
            'hash': hash_value,
            'salt': salt
        }
    
    def verify_hashed_data(self, data: str, hash_value: str, salt: str) -> bool:
        """
        Verify hashed data.
        
        Args:
            data: Original data
            hash_value: Hash to verify against
            salt: Salt used in hashing
            
        Returns:
            True if data matches hash
        """
        computed_hash = self.hash_sensitive_data(data, salt)['hash']
        return self.secure_compare(computed_hash, hash_value)
    
    def generate_csrf_token(self, session_key: str = None) -> str:
        """
        Generate CSRF token.
        
        Args:
            session_key: Session key for token generation
            
        Returns:
            CSRF token
        """
        if session_key is None:
            session_key = secrets.token_hex(16)
        
        # Create token based on session key and timestamp
        import time
        timestamp = str(int(time.time()))
        token_data = f"{session_key}:{timestamp}"
        
        return base64.b64encode(token_data.encode('utf-8')).decode('utf-8')
    
    def validate_csrf_token(self, token: str, session_key: str, max_age: int = 3600) -> bool:
        """
        Validate CSRF token.
        
        Args:
            token: CSRF token to validate
            session_key: Session key
            max_age: Maximum token age in seconds
            
        Returns:
            True if token is valid
        """
        try:
            # Decode token
            token_data = base64.b64decode(token.encode('utf-8')).decode('utf-8')
            stored_session, timestamp = token_data.split(':')
            
            # Verify session key
            if not self.secure_compare(stored_session, session_key):
                return False
            
            # Check token age
            import time
            token_age = time.time() - int(timestamp)
            if token_age > max_age:
                return False
            
            return True
            
        except Exception:
            return False
    
    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename for security.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        if not filename:
            return 'unnamed_file'
        
        # Remove path separators and dangerous characters
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        filename = re.sub(r'\.\.+', '.', filename)  # Remove multiple dots
        filename = filename.strip('. ')  # Remove leading/trailing dots and spaces
        
        # Limit length
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:250] + ('.' + ext if ext else '')
        
        # Ensure filename is not empty
        if not filename:
            filename = 'unnamed_file'
        
        return filename
    
    def validate_file_upload(self, file_obj, allowed_extensions: List[str] = None, 
                           max_size: int = None) -> Dict[str, Any]:
        """
        Validate file upload security.
        
        Args:
            file_obj: Uploaded file object
            allowed_extensions: List of allowed file extensions
            max_size: Maximum file size in bytes
            
        Returns:
            Dict with validation results
        """
        result = {
            'is_valid': False,
            'is_secure': False,
            'issues': [],
            'warnings': [],
            'file_info': {}
        }
        
        if not file_obj:
            result['issues'].append('No file provided')
            return result
        
        # Get file info
        result['file_info'] = {
            'name': file_obj.name,
            'size': file_obj.size,
            'content_type': getattr(file_obj, 'content_type', None)
        }
        
        # Validate file size
        if max_size and file_obj.size > max_size:
            result['issues'].append(f'File size exceeds maximum ({max_size} bytes)')
            return result
        
        # Validate file extension
        if allowed_extensions:
            file_ext = file_obj.name.split('.')[-1].lower() if '.' in file_obj.name else ''
            if file_ext not in [ext.lower() for ext in allowed_extensions]:
                result['issues'].append(f'File extension not allowed: {file_ext}')
                return result
        
        # Sanitize filename
        sanitized_name = self.sanitize_filename(file_obj.name)
        if sanitized_name != file_obj.name:
            result['warnings'].append('Filename was sanitized')
            result['file_info']['sanitized_name'] = sanitized_name
        
        result['is_valid'] = True
        result['is_secure'] = True
        
        return result


# Global security helpers instance
security_helpers = SecurityValidationHelpers()


# Convenience functions
def validate_email_security(email: str) -> Dict[str, Any]:
    """Validate email with security checks."""
    return security_helpers.validate_email_security(email)


def validate_url_security(url: str) -> Dict[str, Any]:
    """Validate URL with security checks."""
    return security_helpers.validate_url_security(url)


def contains_xss_patterns(text: str) -> bool:
    """Check for XSS patterns."""
    return security_helpers.contains_xss_patterns(text)


def contains_sql_patterns(text: str) -> bool:
    """Check for SQL injection patterns."""
    return security_helpers.contains_sql_patterns(text)


def generate_secure_token(length: int = 32) -> str:
    """Generate secure token."""
    return security_helpers.generate_secure_token(length)


def generate_secure_password(length: int = 16) -> str:
    """Generate secure password."""
    return security_helpers.generate_secure_password(length)


def secure_compare(a: str, b: str) -> bool:
    """Timing-safe string comparison."""
    return security_helpers.secure_compare(a, b)


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for security."""
    return security_helpers.sanitize_filename(filename)
