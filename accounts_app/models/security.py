# --- Standard Library Imports ---
from datetime import timedelta

# --- Django Imports ---
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


# --- Login History & Security Alerts ---

class LoginHistory(models.Model):
    """Audit trail for authentication attempts with threat detection"""
    user = models.ForeignKey(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='login_history'
    )
    timestamp = models.DateTimeField(_('timestamp'), auto_now_add=True)
    ip_address = models.GenericIPAddressField(_('IP address'))
    user_agent = models.TextField(_('user agent'), blank=True)
    is_successful = models.BooleanField(_('successful'), default=False)

    class Meta:
        verbose_name = _('Login History')
        verbose_name_plural = _('Login History Records')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp'], name='login_timestamp_idx'),
            models.Index(fields=['ip_address'], name='login_ip_idx'),
            models.Index(fields=['is_successful'], name='login_success_idx'),
        ]

    def __str__(self):
        return f"{self.user.email} @ {self.timestamp}"

    @classmethod
    def purge_old_records(cls, days=90):
        """Remove records older than specified days"""
        cutoff = timezone.now() - timedelta(days=days)
        cls.objects.filter(timestamp__lt=cutoff).delete()

    @classmethod
    def detect_suspicious_activity(cls, ip_address, user=None):
        """Identify and alert on suspicious login patterns"""
        time_window = timezone.now() - timedelta(hours=1)
        failed_attempts = cls.objects.filter(
            ip_address=ip_address,
            is_successful=False,
            timestamp__gte=time_window
        ).count()
        
        # Threshold configuration
        thresholds = {
            5: LoginAlert.HIGH,
            3: LoginAlert.MEDIUM
        }
        
        for count, severity in thresholds.items():
            if failed_attempts >= count:
                LoginAlert.objects.get_or_create(
                    ip_address=ip_address,
                    user=user,
                    alert_type=LoginAlert.MULTIPLE_FAILURES,
                    defaults={
                        'severity': severity,
                        'description': f'{failed_attempts} failed attempts from {ip_address}',
                        'attempt_count': failed_attempts
                    }
                )
                return True
        return False


class LoginAlert(models.Model):
    """Security alert system for authentication anomalies"""
    # Alert Types
    MULTIPLE_FAILURES = 'multiple_failures'
    SUSPICIOUS_IP = 'suspicious_ip'
    UNUSUAL_LOCATION = 'unusual_location'
    BRUTE_FORCE = 'brute_force'
    
    ALERT_TYPES = (
        (MULTIPLE_FAILURES, _('Multiple Failed Attempts')),
        (SUSPICIOUS_IP, _('Suspicious IP Address')),
        (UNUSUAL_LOCATION, _('Unusual Login Location')),
        (BRUTE_FORCE, _('Brute Force Attack')),
    )
    
    # Severity Levels
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'
    
    SEVERITY_LEVELS = (
        (LOW, _('Low')),
        (MEDIUM, _('Medium')),
        (HIGH, _('High')),
        (CRITICAL, _('Critical')),
    )

    user = models.ForeignKey(
        'CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='alerts',
        verbose_name=_('associated user')
    )
    ip_address = models.GenericIPAddressField(_('source IP'))
    alert_type = models.CharField(_('type'), max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(_('severity'), max_length=10, choices=SEVERITY_LEVELS, default=MEDIUM)
    description = models.TextField(_('details'))
    attempt_count = models.PositiveIntegerField(_('attempts'), default=1)
    is_resolved = models.BooleanField(_('resolved'), default=False)
    resolved_by = models.ForeignKey(
        'CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_alerts',
        verbose_name=_('resolved by')
    )
    resolved_at = models.DateTimeField(_('resolution time'), null=True, blank=True)
    resolution_notes = models.TextField(_('resolution notes'), blank=True)
    created = models.DateTimeField(_('created at'), auto_now_add=True)
    updated = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Security Alert')
        verbose_name_plural = _('Security Alerts')
        ordering = ['-created']
        unique_together = ['ip_address', 'alert_type', 'is_resolved']

    def __str__(self):
        return f"{self.get_alert_type_display()} ({self.get_severity_display()})"

    def resolve(self, resolver, notes=''):
        """Mark alert as resolved with audit trail"""
        self.is_resolved = True
        self.resolved_by = resolver
        self.resolved_at = timezone.now()
        self.resolution_notes = notes
        self.save()

    @property
    def is_critical(self):
        """Check if alert requires immediate attention"""
        return self.severity in [self.HIGH, self.CRITICAL]


class PasswordHistory(models.Model):
    """
    Track password history for users to prevent password reuse.

    This model stores hashed passwords to enforce password history policies
    without storing actual passwords in plaintext.
    """
    user = models.ForeignKey(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='password_history'
    )
    password_hash = models.CharField(
        _('password hash'),
        max_length=128,
        help_text=_('Hashed password for history tracking')
    )
    created_at = models.DateTimeField(
        _('created at'),
        auto_now_add=True
    )

    # Security metadata
    ip_address = models.GenericIPAddressField(
        _('IP address'),
        null=True,
        blank=True,
        help_text=_('IP address when password was changed')
    )
    user_agent = models.TextField(
        _('user agent'),
        blank=True,
        help_text=_('User agent when password was changed')
    )

    class Meta:
        verbose_name = _('Password History')
        verbose_name_plural = _('Password History')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
        ]

    def __str__(self):
        return f"Password history for {self.user.email} at {self.created_at}"

    @classmethod
    def cleanup_old_entries(cls, user, keep_count=5):
        """
        Clean up old password history entries, keeping only the most recent ones.

        Args:
            user: User object
            keep_count: Number of entries to keep
        """
        old_entries = cls.objects.filter(user=user).order_by('-created_at')[keep_count:]
        for entry in old_entries:
            entry.delete()


class PasswordSecurityEvent(models.Model):
    """
    Track password-related security events.

    This model logs various password security events for monitoring
    and audit purposes.
    """
    # Event types
    PASSWORD_CHANGED = 'password_changed'
    PASSWORD_RESET_REQUESTED = 'password_reset_requested'
    PASSWORD_RESET_COMPLETED = 'password_reset_completed'
    WEAK_PASSWORD_DETECTED = 'weak_password_detected'
    BREACHED_PASSWORD_DETECTED = 'breached_password_detected'
    PASSWORD_REUSE_ATTEMPTED = 'password_reuse_attempted'
    PASSWORD_POLICY_VIOLATION = 'password_policy_violation'

    EVENT_TYPES = (
        (PASSWORD_CHANGED, _('Password Changed')),
        (PASSWORD_RESET_REQUESTED, _('Password Reset Requested')),
        (PASSWORD_RESET_COMPLETED, _('Password Reset Completed')),
        (WEAK_PASSWORD_DETECTED, _('Weak Password Detected')),
        (BREACHED_PASSWORD_DETECTED, _('Breached Password Detected')),
        (PASSWORD_REUSE_ATTEMPTED, _('Password Reuse Attempted')),
        (PASSWORD_POLICY_VIOLATION, _('Password Policy Violation')),
    )

    user = models.ForeignKey(
        'CustomUser',
        on_delete=models.CASCADE,
        related_name='password_security_events',
        null=True,
        blank=True
    )
    event_type = models.CharField(
        _('event type'),
        max_length=50,
        choices=EVENT_TYPES
    )
    timestamp = models.DateTimeField(
        _('timestamp'),
        auto_now_add=True
    )

    # Request context
    ip_address = models.GenericIPAddressField(
        _('IP address'),
        null=True,
        blank=True
    )
    user_agent = models.TextField(
        _('user agent'),
        blank=True
    )

    # Event details
    details = models.JSONField(
        _('event details'),
        default=dict,
        blank=True,
        help_text=_('Additional event information')
    )

    # Security flags
    is_suspicious = models.BooleanField(
        _('is suspicious'),
        default=False,
        help_text=_('Whether this event is considered suspicious')
    )

    class Meta:
        verbose_name = _('Password Security Event')
        verbose_name_plural = _('Password Security Events')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['event_type', '-timestamp']),
            models.Index(fields=['is_suspicious', '-timestamp']),
        ]

    def __str__(self):
        user_info = self.user.email if self.user else 'Anonymous'
        return f"{self.get_event_type_display()} - {user_info} at {self.timestamp}"

    @classmethod
    def log_event(cls, event_type, user=None, request=None, details=None, is_suspicious=False):
        """
        Log a password security event.

        Args:
            event_type: Type of event
            user: User object (optional)
            request: Django request object (optional)
            details: Additional event details (optional)
            is_suspicious: Whether event is suspicious (default: False)
        """
        from ..utils.security_utils import get_client_ip

        event_data = {
            'event_type': event_type,
            'user': user,
            'details': details or {},
            'is_suspicious': is_suspicious,
        }

        if request:
            event_data.update({
                'ip_address': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })

        return cls.objects.create(**event_data)

    @classmethod
    def unresolved_count(cls):
        """Count of pending alerts"""
        return cls.objects.filter(is_resolved=False).count()

    @classmethod
    def critical_count(cls):
        """Count of high-priority alerts"""
        return cls.objects.filter(
            is_resolved=False,
            severity__in=[cls.HIGH, cls.CRITICAL]
        ).count()
