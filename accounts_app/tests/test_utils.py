# --- Standard Library Imports ---
import os
import json
import time
from typing import Dict, Any, List, Optional, Callable
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager

# --- Django Setup ---
import django
from django.conf import settings
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
    django.setup()

# --- Django Imports ---
from django.test import TestCase, Client, RequestFactory
from django.contrib.auth import get_user_model
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.messages.middleware import MessageMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from django.core import mail
from django.core.cache import cache
from django.urls import reverse
from django.utils import timezone

# --- Third-Party Imports ---
from allauth.socialaccount.models import SocialLogin, SocialAccount
from allauth.account.models import EmailAddress, EmailConfirmation

# --- Local App Imports ---
from .test_factories import TestDataGenerator
from ..models import UserSession, SessionSecurityEvent

User = get_user_model()


class AuthTestMixin:
    """Mixin providing common authentication testing utilities."""
    
    def setUp(self):
        """Set up common test dependencies."""
        super().setUp()
        self.client = Client()
        self.factory = RequestFactory()
        self.test_data = TestDataGenerator()
        
        # Clear cache and mail outbox
        cache.clear()
        mail.outbox = []
    
    def create_authenticated_request(self, user: User = None, path: str = '/', method: str = 'GET', **kwargs):
        """Create an authenticated request with proper middleware."""
        if not user:
            user = self.test_data.create_complete_customer()
        
        if method.upper() == 'GET':
            request = self.factory.get(path, **kwargs)
        elif method.upper() == 'POST':
            request = self.factory.post(path, **kwargs)
        else:
            request = getattr(self.factory, method.lower())(path, **kwargs)
        
        # Add middleware
        request.user = user
        request.session = {}
        
        # Process middleware
        SessionMiddleware(lambda r: None).process_request(request)
        MessageMiddleware(lambda r: None).process_request(request)
        AuthenticationMiddleware(lambda r: None).process_request(request)
        
        return request
    
    def login_user(self, user: User = None, password: str = 'TestPass123!') -> User:
        """Log in a user and return the user object."""
        if not user:
            user = self.test_data.create_complete_customer()
            user.set_password(password)
            user.save()
        
        login_success = self.client.login(email=user.email, password=password)
        self.assertTrue(login_success, f"Failed to login user {user.email}")
        return user
    
    def assert_redirects_to_login(self, response, msg: str = None):
        """Assert that response redirects to login page."""
        login_url = reverse('account_login')
        self.assertRedirects(
            response, 
            f"{login_url}?next={response.wsgi_request.path}",
            msg_prefix=msg
        )
    
    def assert_form_errors(self, response, field_errors: Dict[str, List[str]] = None):
        """Assert that form has specific errors."""
        self.assertContains(response, 'error', msg_prefix="Expected form errors")
        
        if field_errors:
            form = response.context.get('form')
            self.assertIsNotNone(form, "No form found in context")
            
            for field, expected_errors in field_errors.items():
                self.assertIn(field, form.errors, f"No errors for field {field}")
                for error in expected_errors:
                    self.assertIn(error, str(form.errors[field]))
    
    def assert_email_sent(self, count: int = 1, subject_contains: str = None, to_email: str = None):
        """Assert that emails were sent with specific criteria."""
        self.assertEqual(len(mail.outbox), count, f"Expected {count} emails, got {len(mail.outbox)}")
        
        if count > 0:
            email = mail.outbox[-1]  # Get latest email
            
            if subject_contains:
                self.assertIn(subject_contains.lower(), email.subject.lower())
            
            if to_email:
                self.assertIn(to_email, email.to)
    
    def assert_security_event_logged(self, user: User, event_type: str, count: int = 1):
        """Assert that security events were logged."""
        events = SessionSecurityEvent.objects.filter(user=user, event_type=event_type)
        self.assertEqual(events.count(), count, 
                        f"Expected {count} {event_type} events, got {events.count()}")


class MockSocialLoginMixin:
    """Mixin for creating mock social login objects."""
    
    def create_mock_sociallogin(self, provider: str = 'google', user_data: Dict = None) -> Mock:
        """Create a mock SocialLogin object."""
        if user_data is None:
            user_data = {
                'email': '<EMAIL>',
                'given_name': 'John',
                'family_name': 'Doe',
                'picture': 'https://example.com/avatar.jpg',
                'verified_email': True
            }
        
        mock_sociallogin = Mock(spec=SocialLogin)
        mock_sociallogin.account = Mock(spec=SocialAccount)
        mock_sociallogin.account.provider = provider
        mock_sociallogin.account.extra_data = user_data
        mock_sociallogin.user = User(email=user_data.get('email', '<EMAIL>'))
        
        return mock_sociallogin
    
    def create_social_account(self, user: User, provider: str = 'google', extra_data: Dict = None) -> SocialAccount:
        """Create a real SocialAccount for testing."""
        if extra_data is None:
            extra_data = {
                'email': user.email,
                'given_name': user.first_name or 'John',
                'family_name': user.last_name or 'Doe',
            }
        
        return SocialAccount.objects.create(
            user=user,
            provider=provider,
            uid=f'{provider}_{user.id}',
            extra_data=extra_data
        )


class SecurityTestMixin:
    """Mixin for security testing utilities."""
    
    def test_csrf_protection(self, url: str, method: str = 'POST', data: Dict = None):
        """Test CSRF protection on a view."""
        if data is None:
            data = {}
        
        # Test without CSRF token
        if method.upper() == 'POST':
            response = self.client.post(url, data)
        else:
            response = getattr(self.client, method.lower())(url, data)
        
        # Should be forbidden or redirect
        self.assertIn(response.status_code, [403, 302])
    
    def test_rate_limiting(self, url: str, method: str = 'POST', data: Dict = None, limit: int = 5):
        """Test rate limiting on a view."""
        if data is None:
            data = {}
        
        # Clear cache
        cache.clear()
        
        # Make requests up to limit
        for i in range(limit + 1):
            if method.upper() == 'POST':
                response = self.client.post(url, data)
            else:
                response = getattr(self.client, method.lower())(url, data)
            
            if i < limit:
                # Should not be rate limited yet
                self.assertNotEqual(response.status_code, 429)
            else:
                # Should be rate limited now
                self.assertEqual(response.status_code, 429)
    
    def test_xss_protection(self, url: str, form_fields: List[str], method: str = 'POST'):
        """Test XSS protection on form fields."""
        xss_payloads = self.test_data.generate_security_test_data()['xss_payloads']
        
        for payload in xss_payloads:
            for field in form_fields:
                data = {field: payload}
                
                if method.upper() == 'POST':
                    response = self.client.post(url, data)
                else:
                    response = getattr(self.client, method.lower())(url, data)
                
                # Check that payload is not reflected unescaped
                self.assertNotContains(response, payload, html=False)
    
    def test_sql_injection_protection(self, url: str, form_fields: List[str], method: str = 'POST'):
        """Test SQL injection protection on form fields."""
        sql_payloads = self.test_data.generate_security_test_data()['sql_injection_payloads']
        
        for payload in sql_payloads:
            for field in form_fields:
                data = {field: payload}
                
                try:
                    if method.upper() == 'POST':
                        response = self.client.post(url, data)
                    else:
                        response = getattr(self.client, method.lower())(url, data)
                    
                    # Should not cause server error
                    self.assertNotEqual(response.status_code, 500)
                except Exception as e:
                    self.fail(f"SQL injection payload caused exception: {e}")


class PerformanceTestMixin:
    """Mixin for performance testing utilities."""
    
    @contextmanager
    def assert_max_queries(self, max_queries: int):
        """Context manager to assert maximum number of database queries."""
        from django.test.utils import override_settings
        from django.db import connection
        
        with override_settings(DEBUG=True):
            initial_queries = len(connection.queries)
            yield
            final_queries = len(connection.queries)
            query_count = final_queries - initial_queries
            
            self.assertLessEqual(
                query_count, 
                max_queries,
                f"Expected at most {max_queries} queries, got {query_count}"
            )
    
    def measure_response_time(self, url: str, method: str = 'GET', data: Dict = None, max_time: float = 1.0):
        """Measure response time and assert it's within limits."""
        start_time = time.time()
        
        if method.upper() == 'GET':
            response = self.client.get(url, data or {})
        elif method.upper() == 'POST':
            response = self.client.post(url, data or {})
        else:
            response = getattr(self.client, method.lower())(url, data or {})
        
        end_time = time.time()
        response_time = end_time - start_time
        
        self.assertLess(
            response_time, 
            max_time,
            f"Response time {response_time:.3f}s exceeded limit {max_time}s"
        )
        
        return response, response_time


class AccessibilityTestMixin:
    """Mixin for accessibility testing utilities."""
    
    def assert_form_accessibility(self, response, form_selector: str = 'form'):
        """Assert that forms meet accessibility standards."""
        content = response.content.decode()
        
        # Check for form labels
        self.assertIn('<label', content, "Forms should have labels")
        
        # Check for required field indicators
        if 'required' in content:
            self.assertIn('aria-required', content, "Required fields should have aria-required")
        
        # Check for error handling
        if 'error' in content:
            self.assertIn('aria-invalid', content, "Error fields should have aria-invalid")
    
    def assert_keyboard_navigation(self, response):
        """Assert that page supports keyboard navigation."""
        content = response.content.decode()
        
        # Check for tabindex or focusable elements
        focusable_elements = ['input', 'button', 'select', 'textarea', 'a href']
        has_focusable = any(element in content for element in focusable_elements)
        self.assertTrue(has_focusable, "Page should have focusable elements")
    
    def assert_screen_reader_support(self, response):
        """Assert that page supports screen readers."""
        content = response.content.decode()
        
        # Check for ARIA attributes
        aria_attributes = ['aria-label', 'aria-describedby', 'aria-labelledby', 'role']
        has_aria = any(attr in content for attr in aria_attributes)
        self.assertTrue(has_aria, "Page should have ARIA attributes for screen readers")


class ComprehensiveAuthTestCase(AuthTestMixin, MockSocialLoginMixin, SecurityTestMixin, 
                               PerformanceTestMixin, AccessibilityTestMixin, TestCase):
    """
    Comprehensive test case class combining all authentication testing mixins.
    
    Use this as the base class for authentication tests to get all testing utilities.
    """
    
    def setUp(self):
        """Set up all test dependencies."""
        super().setUp()
        
        # Additional setup for comprehensive testing
        self.maxDiff = None  # Show full diff in assertions
        
        # Create common test users
        self.customer_user = self.test_data.create_complete_customer(email='<EMAIL>')
        self.provider_user = self.test_data.create_complete_service_provider(email='<EMAIL>')
        self.admin_user = self.test_data.create_complete_customer(email='<EMAIL>')
        self.admin_user.role = User.ADMIN
        self.admin_user.is_staff = True
        self.admin_user.is_superuser = True
        self.admin_user.save()
    
    def tearDown(self):
        """Clean up after tests."""
        super().tearDown()
        
        # Clear cache and sessions
        cache.clear()
        
        # Clear mail outbox
        mail.outbox = []


# Utility functions for test configuration
def skip_if_no_social_auth(test_func):
    """Decorator to skip tests if social authentication is not configured."""
    def wrapper(*args, **kwargs):
        if not hasattr(settings, 'SOCIALACCOUNT_PROVIDERS'):
            return
        return test_func(*args, **kwargs)
    return wrapper


def mock_email_backend():
    """Context manager to mock email backend for testing."""
    return patch('django.core.mail.backends.locmem.EmailBackend.send_messages')


def create_test_request(user: User = None, path: str = '/', method: str = 'GET', **kwargs):
    """Create a test request with proper setup."""
    factory = RequestFactory()
    
    if method.upper() == 'GET':
        request = factory.get(path, **kwargs)
    elif method.upper() == 'POST':
        request = factory.post(path, **kwargs)
    else:
        request = getattr(factory, method.lower())(path, **kwargs)
    
    # Add user and session
    request.user = user or User()
    request.session = {}
    
    return request
