# --- Standard Library Imports ---
import time
from unittest.mock import patch, Mock

# --- Django Imports ---
from django.test import TestCase, Client, override_settings
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core import mail
from django.core.cache import cache
from django.contrib.sessions.models import Session
from django.utils import timezone

# --- Third-Party Imports ---
from allauth.account.models import EmailAddress, EmailConfirmation
from allauth.socialaccount.models import SocialAccount, SocialApp

# --- Local App Imports ---
from .test_utils import ComprehensiveAuthTestCase, MockSocialLoginMixin
from .test_factories import TestDataGenerator
from ..models import UserSession, SessionSecurityEvent, LoginHistory

User = get_user_model()


class AuthenticationFlowIntegrationTests(ComprehensiveAuthTestCase):
    """Integration tests for complete authentication flows."""
    
    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
        
        # Clear any existing data
        User.objects.filter(email__contains='test').delete()
        mail.outbox = []
        cache.clear()
    
    def test_complete_customer_registration_flow(self):
        """Test complete customer registration and verification flow."""
        # Step 1: Access signup page
        response = self.client.get(reverse('accounts_app:customer_signup'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Sign Up')
        
        # Step 2: Submit registration form
        signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER, {
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
        })
        
        response = self.client.post(reverse('accounts_app:customer_signup'), signup_data)
        
        # Should redirect after successful signup
        self.assertEqual(response.status_code, 302)
        
        # Step 3: Verify user was created
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.role, User.CUSTOMER)
        self.assertTrue(user.is_active)
        self.assertTrue(hasattr(user, 'customer_profile'))
        
        # Step 4: Verify email was sent
        self.assert_email_sent(count=1, subject_contains='verification')
        
        # Step 5: Verify login works
        login_success = self.client.login(
            email='<EMAIL>', 
            password=signup_data['password1']
        )
        self.assertTrue(login_success)
        
        # Step 6: Verify redirect to dashboard
        response = self.client.get(reverse('accounts_app:dashboard'))
        self.assertEqual(response.status_code, 200)
    
    def test_complete_service_provider_registration_flow(self):
        """Test complete service provider registration and verification flow."""
        # Step 1: Access provider signup page
        response = self.client.get(reverse('accounts_app:provider_signup'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Sign Up')
        
        # Step 2: Submit registration form
        signup_data = self.test_data.generate_form_data('signup', User.SERVICE_PROVIDER, {
            'email': '<EMAIL>',
        })
        
        response = self.client.post(reverse('accounts_app:provider_signup'), signup_data)
        
        # Should redirect after successful signup
        self.assertEqual(response.status_code, 302)
        
        # Step 3: Verify user was created
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.role, User.SERVICE_PROVIDER)
        self.assertFalse(user.is_active)  # Requires email verification
        self.assertTrue(hasattr(user, 'service_provider_profile'))
        
        # Step 4: Verify email was sent
        self.assert_email_sent(count=1, subject_contains='verification')
        
        # Step 5: Verify login fails before verification
        login_success = self.client.login(
            email='<EMAIL>', 
            password=signup_data['password1']
        )
        self.assertFalse(login_success)
        
        # Step 6: Simulate email verification
        user.is_active = True
        user.save()
        
        # Step 7: Verify login works after verification
        login_success = self.client.login(
            email='<EMAIL>', 
            password=signup_data['password1']
        )
        self.assertTrue(login_success)
    
    def test_login_logout_flow(self):
        """Test complete login and logout flow."""
        # Step 1: Access login page
        response = self.client.get(reverse('account_login'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Sign In')
        
        # Step 2: Submit login form
        login_data = {
            'login': self.customer_user.email,
            'password': 'TestPass123!',
        }
        
        response = self.client.post(reverse('account_login'), login_data)
        
        # Should redirect after successful login
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, '/dashboard/')
        
        # Step 3: Verify user is logged in
        response = self.client.get(reverse('accounts_app:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['user'], self.customer_user)
        
        # Step 4: Verify session was created
        sessions = UserSession.objects.filter(user=self.customer_user, is_active=True)
        self.assertGreater(sessions.count(), 0)
        
        # Step 5: Verify security event was logged
        self.assert_security_event_logged(self.customer_user, 'login_success')
        
        # Step 6: Logout
        response = self.client.post(reverse('account_logout'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, '/')
        
        # Step 7: Verify user is logged out
        response = self.client.get(reverse('accounts_app:dashboard'))
        self.assertRedirects(response, '/accounts/login/?next=/dashboard/')
        
        # Step 8: Verify logout event was logged
        self.assert_security_event_logged(self.customer_user, 'logout')
    
    def test_password_reset_flow(self):
        """Test complete password reset flow."""
        # Step 1: Access password reset page
        response = self.client.get(reverse('account_reset_password'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Password Reset')
        
        # Step 2: Submit password reset request
        reset_data = {'email': self.customer_user.email}
        response = self.client.post(reverse('account_reset_password'), reset_data)
        
        # Should redirect to done page
        self.assertEqual(response.status_code, 302)
        
        # Step 3: Verify email was sent
        self.assert_email_sent(count=1, subject_contains='password')
        
        # Step 4: Verify security event was logged
        events = SessionSecurityEvent.objects.filter(
            user=self.customer_user,
            event_type='password_reset_requested'
        )
        # Note: This depends on implementation
    
    def test_email_verification_flow(self):
        """Test email verification flow."""
        # Create unverified user
        user = self.test_data.create_complete_customer(email='<EMAIL>')
        user.is_active = False
        user.save()
        
        # Create email address record
        email_address = EmailAddress.objects.create(
            user=user,
            email=user.email,
            verified=False,
            primary=True
        )
        
        # Step 1: Access verification status page
        self.client.force_login(user)
        response = self.client.get(reverse('accounts_app:email_verification_status'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'verification')
        
        # Step 2: Request verification email resend
        mail.outbox = []
        response = self.client.post(reverse('accounts_app:resend_verification_email'))
        
        # Should send email
        self.assert_email_sent(count=1, subject_contains='verification')
        
        # Step 3: Simulate email verification
        email_address.verified = True
        email_address.save()
        user.is_active = True
        user.save()
        
        # Step 4: Access success page
        response = self.client.get(reverse('accounts_app:email_verification_success'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'verified')
    
    def test_failed_login_attempts_flow(self):
        """Test failed login attempts and lockout flow."""
        login_data = {
            'login': self.customer_user.email,
            'password': 'wrongpassword',
        }
        
        # Step 1: Make multiple failed login attempts
        for i in range(3):
            response = self.client.post(reverse('account_login'), login_data)
            # Should not redirect (failed login)
            self.assertEqual(response.status_code, 200)
            self.assertContains(response, 'error')
        
        # Step 2: Verify failed attempts were logged
        failed_attempts = LoginHistory.objects.filter(
            user=self.customer_user,
            success=False
        )
        self.assertGreaterEqual(failed_attempts.count(), 3)
        
        # Step 3: Verify security events were logged
        events = SessionSecurityEvent.objects.filter(
            user=self.customer_user,
            event_type='login_failed'
        )
        self.assertGreaterEqual(events.count(), 3)
    
    def test_concurrent_session_handling(self):
        """Test concurrent session handling."""
        # Step 1: Login from first "device"
        client1 = Client()
        login_data = {
            'login': self.customer_user.email,
            'password': 'TestPass123!',
        }
        
        response1 = client1.post(reverse('account_login'), login_data)
        self.assertEqual(response1.status_code, 302)
        
        # Step 2: Login from second "device"
        client2 = Client()
        response2 = client2.post(reverse('account_login'), login_data)
        self.assertEqual(response2.status_code, 302)
        
        # Step 3: Verify both sessions exist
        sessions = UserSession.objects.filter(user=self.customer_user, is_active=True)
        self.assertGreaterEqual(sessions.count(), 1)
        
        # Step 4: Verify concurrent login event was logged
        events = SessionSecurityEvent.objects.filter(
            user=self.customer_user,
            event_type='concurrent_login'
        )
        # Note: This depends on implementation
    
    def test_session_timeout_flow(self):
        """Test session timeout handling."""
        # Step 1: Login user
        self.login_user(self.customer_user)
        
        # Step 2: Access protected page
        response = self.client.get(reverse('accounts_app:dashboard'))
        self.assertEqual(response.status_code, 200)
        
        # Step 3: Simulate session timeout
        sessions = UserSession.objects.filter(user=self.customer_user, is_active=True)
        for session in sessions:
            session.expires_at = timezone.now() - timezone.timedelta(hours=1)
            session.save()
        
        # Step 4: Access protected page after timeout
        # Note: This test depends on session middleware implementation
        
    def test_role_based_access_flow(self):
        """Test role-based access control flow."""
        # Step 1: Login as customer
        self.login_user(self.customer_user)
        
        # Step 2: Access customer-allowed pages
        response = self.client.get(reverse('accounts_app:dashboard'))
        self.assertEqual(response.status_code, 200)
        
        # Step 3: Try to access provider-only pages
        # Note: This depends on specific view implementations
        
        # Step 4: Login as provider
        self.client.logout()
        self.login_user(self.provider_user)
        
        # Step 5: Access provider-allowed pages
        response = self.client.get(reverse('accounts_app:dashboard'))
        self.assertEqual(response.status_code, 200)
