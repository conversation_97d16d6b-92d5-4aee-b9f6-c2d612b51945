#!/usr/bin/env python3
"""
Comprehensive test runner for CozyWish authentication system.

This script runs all authentication tests and generates detailed coverage reports.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')

import django
django.setup()

from django.test.utils import get_runner
from django.conf import settings
from django.core.management import execute_from_command_line


class ComprehensiveTestRunner:
    """Comprehensive test runner for authentication system."""
    
    def __init__(self):
        self.project_root = project_root
        self.accounts_app_path = self.project_root / 'accounts_app'
        self.test_results = {}
        
    def run_all_tests(self, verbosity=2, coverage=True, parallel=False):
        """Run all authentication tests."""
        print("🚀 Starting Comprehensive Authentication Tests")
        print("=" * 60)
        
        # Test categories
        test_categories = [
            ('Unit Tests - Adapters', 'accounts_app.tests.test_allauth_adapters'),
            ('Unit Tests - Forms', 'accounts_app.tests.test_allauth_forms'),
            ('Unit Tests - Models', 'accounts_app.tests.test_models'),
            ('Integration Tests', 'accounts_app.tests.test_authentication_integration'),
            ('Security Tests', 'accounts_app.tests.test_authentication_security'),
            ('Performance Tests', 'accounts_app.tests.test_authentication_performance'),
            ('Accessibility Tests', 'accounts_app.tests.test_authentication_accessibility'),
            ('Enhanced Auth Tests', 'accounts_app.tests.test_enhanced_authentication'),
        ]
        
        total_tests = 0
        total_failures = 0
        total_errors = 0
        
        for category_name, test_module in test_categories:
            print(f"\n📋 Running {category_name}")
            print("-" * 40)
            
            result = self._run_test_category(test_module, verbosity, coverage)
            self.test_results[category_name] = result
            
            total_tests += result.get('tests_run', 0)
            total_failures += result.get('failures', 0)
            total_errors += result.get('errors', 0)
            
            if result.get('success', False):
                print(f"✅ {category_name}: PASSED")
            else:
                print(f"❌ {category_name}: FAILED")
        
        # Generate summary report
        self._generate_summary_report(total_tests, total_failures, total_errors)
        
        # Generate coverage report if requested
        if coverage:
            self._generate_coverage_report()
        
        return total_failures == 0 and total_errors == 0
    
    def _run_test_category(self, test_module, verbosity=2, coverage=True):
        """Run a specific test category."""
        try:
            # Prepare test command
            cmd = [
                sys.executable, 
                'manage.py', 
                'test', 
                test_module,
                f'--verbosity={verbosity}',
                '--keepdb',  # Keep test database for faster subsequent runs
            ]
            
            if coverage:
                cmd.extend(['--with-coverage', '--cover-package=accounts_app'])
            
            # Run tests
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            # Parse results
            output = result.stdout + result.stderr
            
            return {
                'success': result.returncode == 0,
                'output': output,
                'tests_run': self._parse_tests_run(output),
                'failures': self._parse_failures(output),
                'errors': self._parse_errors(output),
            }
            
        except Exception as e:
            return {
                'success': False,
                'output': str(e),
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
            }
    
    def _parse_tests_run(self, output):
        """Parse number of tests run from output."""
        import re
        match = re.search(r'Ran (\d+) test', output)
        return int(match.group(1)) if match else 0
    
    def _parse_failures(self, output):
        """Parse number of failures from output."""
        import re
        match = re.search(r'failures=(\d+)', output)
        return int(match.group(1)) if match else 0
    
    def _parse_errors(self, output):
        """Parse number of errors from output."""
        import re
        match = re.search(r'errors=(\d+)', output)
        return int(match.group(1)) if match else 0
    
    def _generate_summary_report(self, total_tests, total_failures, total_errors):
        """Generate summary report."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY REPORT")
        print("=" * 60)
        
        print(f"Total Tests Run: {total_tests}")
        print(f"Failures: {total_failures}")
        print(f"Errors: {total_errors}")
        print(f"Success Rate: {((total_tests - total_failures - total_errors) / total_tests * 100):.1f}%" if total_tests > 0 else "N/A")
        
        print("\n📋 Category Breakdown:")
        for category, result in self.test_results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            tests = result.get('tests_run', 0)
            failures = result.get('failures', 0)
            errors = result.get('errors', 0)
            print(f"  {category}: {status} ({tests} tests, {failures} failures, {errors} errors)")
        
        # Overall result
        if total_failures == 0 and total_errors == 0:
            print("\n🎉 ALL TESTS PASSED!")
        else:
            print(f"\n⚠️  {total_failures + total_errors} TESTS FAILED")
    
    def _generate_coverage_report(self):
        """Generate coverage report."""
        print("\n" + "=" * 60)
        print("📈 GENERATING COVERAGE REPORT")
        print("=" * 60)
        
        try:
            # Run coverage report
            cmd = [
                'coverage', 'run', 
                '--source=accounts_app',
                '--omit=*/migrations/*,*/tests/*,*/venv/*,*/env/*',
                'manage.py', 'test', 'accounts_app'
            ]
            
            subprocess.run(cmd, cwd=self.project_root, check=True)
            
            # Generate HTML report
            subprocess.run(['coverage', 'html'], cwd=self.project_root, check=True)
            
            # Generate console report
            result = subprocess.run(
                ['coverage', 'report'], 
                cwd=self.project_root, 
                capture_output=True, 
                text=True
            )
            
            print(result.stdout)
            
            # Check coverage percentage
            coverage_match = re.search(r'TOTAL\s+\d+\s+\d+\s+(\d+)%', result.stdout)
            if coverage_match:
                coverage_percent = int(coverage_match.group(1))
                if coverage_percent >= 90:
                    print(f"🎯 Excellent! Coverage: {coverage_percent}% (Target: 90%+)")
                elif coverage_percent >= 80:
                    print(f"👍 Good coverage: {coverage_percent}% (Target: 90%+)")
                else:
                    print(f"⚠️  Low coverage: {coverage_percent}% (Target: 90%+)")
            
            print(f"\n📄 Detailed HTML report: {self.project_root}/htmlcov/index.html")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Coverage report generation failed: {e}")
        except FileNotFoundError:
            print("❌ Coverage tool not found. Install with: pip install coverage")
    
    def run_specific_tests(self, test_pattern, verbosity=2):
        """Run specific tests matching a pattern."""
        print(f"🔍 Running tests matching: {test_pattern}")
        
        cmd = [
            sys.executable, 
            'manage.py', 
            'test', 
            test_pattern,
            f'--verbosity={verbosity}',
        ]
        
        result = subprocess.run(cmd, cwd=self.project_root)
        return result.returncode == 0
    
    def run_security_tests_only(self):
        """Run only security-related tests."""
        print("🔒 Running Security Tests Only")
        return self.run_specific_tests('accounts_app.tests.test_authentication_security')
    
    def run_performance_tests_only(self):
        """Run only performance tests."""
        print("⚡ Running Performance Tests Only")
        return self.run_specific_tests('accounts_app.tests.test_authentication_performance')
    
    def run_accessibility_tests_only(self):
        """Run only accessibility tests."""
        print("♿ Running Accessibility Tests Only")
        return self.run_specific_tests('accounts_app.tests.test_authentication_accessibility')
    
    def check_test_environment(self):
        """Check if test environment is properly configured."""
        print("🔧 Checking Test Environment")
        print("-" * 30)
        
        checks = []
        
        # Check Django settings
        try:
            from django.conf import settings
            checks.append(("Django Settings", "✅ OK"))
        except Exception as e:
            checks.append(("Django Settings", f"❌ {e}"))
        
        # Check database
        try:
            from django.db import connection
            connection.ensure_connection()
            checks.append(("Database Connection", "✅ OK"))
        except Exception as e:
            checks.append(("Database Connection", f"❌ {e}"))
        
        # Check required packages
        required_packages = [
            'django-allauth',
            'factory-boy',
            'beautifulsoup4',
            'psutil',
        ]
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                checks.append((f"Package: {package}", "✅ OK"))
            except ImportError:
                checks.append((f"Package: {package}", "❌ Missing"))
        
        # Print results
        for check_name, status in checks:
            print(f"  {check_name}: {status}")
        
        # Check if all passed
        all_passed = all("✅" in status for _, status in checks)
        
        if all_passed:
            print("\n✅ Test environment is ready!")
        else:
            print("\n❌ Test environment has issues. Please fix before running tests.")
        
        return all_passed


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Run comprehensive authentication tests')
    parser.add_argument('--category', choices=[
        'all', 'unit', 'integration', 'security', 'performance', 'accessibility'
    ], default='all', help='Test category to run')
    parser.add_argument('--verbosity', type=int, default=2, help='Test verbosity level')
    parser.add_argument('--no-coverage', action='store_true', help='Skip coverage report')
    parser.add_argument('--parallel', action='store_true', help='Run tests in parallel')
    parser.add_argument('--check-env', action='store_true', help='Check test environment only')
    parser.add_argument('--pattern', help='Run tests matching specific pattern')
    
    args = parser.parse_args()
    
    runner = ComprehensiveTestRunner()
    
    # Check environment if requested
    if args.check_env:
        return 0 if runner.check_test_environment() else 1
    
    # Run specific pattern if provided
    if args.pattern:
        return 0 if runner.run_specific_tests(args.pattern, args.verbosity) else 1
    
    # Run specific category
    if args.category == 'security':
        return 0 if runner.run_security_tests_only() else 1
    elif args.category == 'performance':
        return 0 if runner.run_performance_tests_only() else 1
    elif args.category == 'accessibility':
        return 0 if runner.run_accessibility_tests_only() else 1
    else:
        # Run all tests
        success = runner.run_all_tests(
            verbosity=args.verbosity,
            coverage=not args.no_coverage,
            parallel=args.parallel
        )
        return 0 if success else 1


if __name__ == '__main__':
    import re
    sys.exit(main())
