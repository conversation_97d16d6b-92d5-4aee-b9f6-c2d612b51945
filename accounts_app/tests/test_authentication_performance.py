# --- Standard Library Imports ---
import time
from unittest.mock import patch

# --- Django Imports ---
from django.test import TestCase, Client, override_settings
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core import mail
from django.db import connection
from django.test.utils import override_settings

# --- Local App Imports ---
from .test_utils import ComprehensiveAuthTestCase, PerformanceTestMixin
from .test_factories import TestDataGenerator

User = get_user_model()


class AuthenticationPerformanceTests(ComprehensiveAuthTestCase, PerformanceTestMixin):
    """Performance tests for authentication system."""
    
    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
        
        # Create additional test users for performance testing
        self.test_users = []
        for i in range(10):
            user = self.test_data.create_complete_customer(
                email=f'perftest{i}@example.com'
            )
            user.set_password('TestPass123!')
            user.save()
            self.test_users.append(user)
    
    def test_login_page_performance(self):
        """Test login page loading performance."""
        url = reverse('account_login')
        
        # Test response time
        response, response_time = self.measure_response_time(url, 'GET', max_time=2.0)
        self.assertEqual(response.status_code, 200)
        
        # Test database queries
        with self.assert_max_queries(5):
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)
    
    def test_signup_page_performance(self):
        """Test signup page loading performance."""
        urls = [
            reverse('accounts_app:customer_signup'),
            reverse('accounts_app:provider_signup'),
        ]
        
        for url in urls:
            with self.subTest(url=url):
                # Test response time
                response, response_time = self.measure_response_time(url, 'GET', max_time=2.0)
                self.assertEqual(response.status_code, 200)
                
                # Test database queries
                with self.assert_max_queries(5):
                    response = self.client.get(url)
                    self.assertEqual(response.status_code, 200)
    
    def test_login_processing_performance(self):
        """Test login processing performance."""
        url = reverse('account_login')
        login_data = {
            'login': self.customer_user.email,
            'password': 'TestPass123!',
        }
        
        # Test response time
        response, response_time = self.measure_response_time(
            url, 'POST', login_data, max_time=3.0
        )
        self.assertEqual(response.status_code, 302)
        
        # Test database queries
        with self.assert_max_queries(15):  # Allow more queries for authentication
            response = self.client.post(url, login_data)
            self.assertEqual(response.status_code, 302)
    
    def test_signup_processing_performance(self):
        """Test signup processing performance."""
        url = reverse('accounts_app:customer_signup')
        signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER, {
            'email': '<EMAIL>'
        })
        
        # Test response time
        response, response_time = self.measure_response_time(
            url, 'POST', signup_data, max_time=5.0
        )
        self.assertIn(response.status_code, [200, 302])
        
        # Test database queries
        with self.assert_max_queries(20):  # Allow more queries for user creation
            signup_data['email'] = '<EMAIL>'
            response = self.client.post(url, signup_data)
            self.assertIn(response.status_code, [200, 302])
    
    def test_password_reset_performance(self):
        """Test password reset performance."""
        url = reverse('account_reset_password')
        reset_data = {'email': self.customer_user.email}
        
        # Test response time
        response, response_time = self.measure_response_time(
            url, 'POST', reset_data, max_time=3.0
        )
        self.assertEqual(response.status_code, 302)
        
        # Test database queries
        with self.assert_max_queries(10):
            response = self.client.post(url, reset_data)
            self.assertEqual(response.status_code, 302)
    
    def test_email_sending_performance(self):
        """Test email sending performance."""
        # Test signup email sending
        signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER, {
            'email': '<EMAIL>'
        })
        
        start_time = time.time()
        response = self.client.post(reverse('accounts_app:customer_signup'), signup_data)
        end_time = time.time()
        
        # Email sending should not significantly slow down response
        response_time = end_time - start_time
        self.assertLess(response_time, 5.0, "Email sending is too slow")
        
        # Verify email was sent
        self.assertGreater(len(mail.outbox), 0)
    
    def test_concurrent_login_performance(self):
        """Test performance under concurrent login load."""
        import threading
        import queue
        
        results = queue.Queue()
        
        def login_worker(user_index):
            """Worker function for concurrent login testing."""
            client = Client()
            user = self.test_users[user_index % len(self.test_users)]
            
            start_time = time.time()
            response = client.post(reverse('account_login'), {
                'login': user.email,
                'password': 'TestPass123!',
            })
            end_time = time.time()
            
            results.put({
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'user_index': user_index
            })
        
        # Create and start threads
        threads = []
        num_threads = 5
        
        for i in range(num_threads):
            thread = threading.Thread(target=login_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Analyze results
        response_times = []
        while not results.empty():
            result = results.get()
            self.assertEqual(result['status_code'], 302)
            response_times.append(result['response_time'])
        
        # Check that all requests completed reasonably quickly
        max_response_time = max(response_times)
        avg_response_time = sum(response_times) / len(response_times)
        
        self.assertLess(max_response_time, 10.0, "Concurrent login too slow")
        self.assertLess(avg_response_time, 5.0, "Average concurrent login too slow")
    
    def test_database_query_optimization(self):
        """Test database query optimization in authentication flows."""
        # Test login query optimization
        with self.assert_max_queries(10):
            response = self.client.post(reverse('account_login'), {
                'login': self.customer_user.email,
                'password': 'TestPass123!',
            })
            self.assertEqual(response.status_code, 302)
        
        # Test dashboard access query optimization
        self.login_user(self.customer_user)
        with self.assert_max_queries(8):
            response = self.client.get(reverse('accounts_app:dashboard'))
            self.assertEqual(response.status_code, 200)
    
    def test_template_rendering_performance(self):
        """Test template rendering performance."""
        # Test login template
        start_time = time.time()
        response = self.client.get(reverse('account_login'))
        end_time = time.time()
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(end_time - start_time, 1.0, "Login template rendering too slow")
        
        # Test signup template
        start_time = time.time()
        response = self.client.get(reverse('accounts_app:customer_signup'))
        end_time = time.time()
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(end_time - start_time, 1.0, "Signup template rendering too slow")
    
    def test_session_management_performance(self):
        """Test session management performance."""
        # Login user
        self.login_user(self.customer_user)
        
        # Test session access performance
        start_time = time.time()
        for i in range(10):
            response = self.client.get(reverse('accounts_app:dashboard'))
            self.assertEqual(response.status_code, 200)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        self.assertLess(avg_time, 0.5, "Session access too slow")
    
    def test_form_validation_performance(self):
        """Test form validation performance."""
        # Test signup form validation
        signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER)
        
        start_time = time.time()
        response = self.client.post(reverse('accounts_app:customer_signup'), signup_data)
        end_time = time.time()
        
        self.assertLess(end_time - start_time, 3.0, "Form validation too slow")
        
        # Test login form validation
        login_data = {
            'login': '<EMAIL>',
            'password': 'wrongpassword',
        }
        
        start_time = time.time()
        response = self.client.post(reverse('account_login'), login_data)
        end_time = time.time()
        
        self.assertLess(end_time - start_time, 2.0, "Login validation too slow")
    
    def test_memory_usage_optimization(self):
        """Test memory usage optimization."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Perform multiple authentication operations
        for i in range(10):
            # Signup
            signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER, {
                'email': f'memtest{i}@example.com'
            })
            self.client.post(reverse('accounts_app:customer_signup'), signup_data)
            
            # Login
            user = User.objects.get(email=f'memtest{i}@example.com')
            user.set_password('TestPass123!')
            user.save()
            
            self.client.post(reverse('account_login'), {
                'login': f'memtest{i}@example.com',
                'password': 'TestPass123!',
            })
            
            # Logout
            self.client.post(reverse('account_logout'))
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 50MB)
        self.assertLess(memory_increase, 50 * 1024 * 1024, "Memory usage too high")
    
    def test_cache_performance(self):
        """Test caching performance improvements."""
        from django.core.cache import cache
        
        # Clear cache
        cache.clear()
        
        # Test without cache
        start_time = time.time()
        response = self.client.get(reverse('account_login'))
        no_cache_time = time.time() - start_time
        
        # Test with cache (second request)
        start_time = time.time()
        response = self.client.get(reverse('account_login'))
        cached_time = time.time() - start_time
        
        # Cached response should be faster or similar
        # (This test depends on actual caching implementation)
        self.assertLessEqual(cached_time, no_cache_time * 1.5)
    
    @override_settings(DEBUG=True)
    def test_debug_mode_performance_impact(self):
        """Test performance impact of debug mode."""
        # This test runs with DEBUG=True to measure impact
        
        # Test login performance in debug mode
        response, response_time = self.measure_response_time(
            reverse('account_login'), 'GET', max_time=5.0
        )
        self.assertEqual(response.status_code, 200)
        
        # Debug mode will be slower, but should still be reasonable
        self.assertLess(response_time, 5.0, "Debug mode performance too slow")
    
    def test_large_dataset_performance(self):
        """Test performance with large datasets."""
        # Create many users to test scalability
        users = []
        for i in range(100):
            user = User.objects.create_user(
                email=f'large_test_{i}@example.com',
                password='TestPass123!',
                role=User.CUSTOMER
            )
            users.append(user)
        
        # Test login performance with large user base
        test_user = users[50]  # Pick a user from the middle
        
        response, response_time = self.measure_response_time(
            reverse('account_login'), 
            'POST', 
            {
                'login': test_user.email,
                'password': 'TestPass123!',
            },
            max_time=5.0
        )
        
        self.assertEqual(response.status_code, 302)
        
        # Cleanup
        User.objects.filter(email__startswith='large_test_').delete()
