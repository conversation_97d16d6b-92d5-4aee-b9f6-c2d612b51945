# --- Standard Library Imports ---
import os
import random
import string
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# --- Django Setup ---
import django
from django.conf import settings
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
    django.setup()

# --- Django Imports ---
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import RequestFactory

# --- Third-Party Imports ---
import factory
from factory.django import DjangoModelFactory
from allauth.socialaccount.models import SocialAccount, SocialApp
from allauth.account.models import EmailAddress

# --- Local App Imports ---
from ..models import (
    CustomerProfile, ServiceProviderProfile, UserSession, 
    SessionSecurityEvent, LoginHistory, ProfilePrivacySettings
)

User = get_user_model()


class UserFactory(DjangoModelFactory):
    """Factory for creating test users."""
    
    class Meta:
        model = User
    
    email = factory.Sequence(lambda n: f'user{n}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    role = User.CUSTOMER
    is_active = True
    is_staff = False
    is_superuser = False
    date_joined = factory.LazyFunction(timezone.now)
    
    @factory.post_generation
    def password(self, create, extracted, **kwargs):
        if not create:
            return
        
        password = extracted or 'TestPass123!'
        self.set_password(password)
        self.save()


class CustomerUserFactory(UserFactory):
    """Factory for creating customer users."""
    role = User.CUSTOMER


class ServiceProviderUserFactory(UserFactory):
    """Factory for creating service provider users."""
    role = User.SERVICE_PROVIDER
    is_active = False  # Requires email verification


class AdminUserFactory(UserFactory):
    """Factory for creating admin users."""
    role = User.ADMIN
    is_staff = True
    is_superuser = True


class CustomerProfileFactory(DjangoModelFactory):
    """Factory for creating customer profiles."""
    
    class Meta:
        model = CustomerProfile
    
    user = factory.SubFactory(CustomerUserFactory)
    phone = factory.Faker('phone_number')
    date_of_birth = factory.Faker('date_of_birth', minimum_age=18, maximum_age=80)
    address = factory.Faker('street_address')
    city = factory.Faker('city')
    state = factory.Faker('state_abbr')
    zip_code = factory.Faker('zipcode')
    preferences = factory.LazyFunction(lambda: {
        'notifications': True,
        'marketing_emails': False,
        'theme': 'light'
    })


class ServiceProviderProfileFactory(DjangoModelFactory):
    """Factory for creating service provider profiles."""
    
    class Meta:
        model = ServiceProviderProfile
    
    user = factory.SubFactory(ServiceProviderUserFactory)
    legal_name = factory.Faker('company')
    business_name = factory.Faker('company')
    phone = factory.Faker('phone_number')
    address = factory.Faker('street_address')
    city = factory.Faker('city')
    state = factory.Faker('state_abbr')
    zip_code = factory.Faker('zipcode')
    business_type = 'venue'
    tax_id = factory.Sequence(lambda n: f'12-345678{n:02d}')
    website = factory.Faker('url')
    description = factory.Faker('text', max_nb_chars=500)


class EmailAddressFactory(DjangoModelFactory):
    """Factory for creating email addresses."""
    
    class Meta:
        model = EmailAddress
    
    user = factory.SubFactory(UserFactory)
    email = factory.LazyAttribute(lambda obj: obj.user.email)
    verified = True
    primary = True


class SocialAppFactory(DjangoModelFactory):
    """Factory for creating social apps."""
    
    class Meta:
        model = SocialApp
    
    provider = 'google'
    name = 'Google'
    client_id = factory.Sequence(lambda n: f'test_client_id_{n}')
    secret = factory.Sequence(lambda n: f'test_secret_{n}')


class SocialAccountFactory(DjangoModelFactory):
    """Factory for creating social accounts."""
    
    class Meta:
        model = SocialAccount
    
    user = factory.SubFactory(UserFactory)
    provider = 'google'
    uid = factory.Sequence(lambda n: f'google_uid_{n}')
    extra_data = factory.LazyFunction(lambda: {
        'email': '<EMAIL>',
        'given_name': 'John',
        'family_name': 'Doe',
        'picture': 'https://example.com/avatar.jpg',
        'verified_email': True
    })


class UserSessionFactory(DjangoModelFactory):
    """Factory for creating user sessions."""
    
    class Meta:
        model = UserSession
    
    user = factory.SubFactory(UserFactory)
    session_key = factory.LazyFunction(lambda: ''.join(random.choices(string.ascii_letters + string.digits, k=40)))
    ip_address = factory.Faker('ipv4')
    user_agent = factory.Faker('user_agent')
    is_active = True
    created_at = factory.LazyFunction(timezone.now)
    last_activity = factory.LazyFunction(timezone.now)
    expires_at = factory.LazyFunction(lambda: timezone.now() + timedelta(hours=24))


class SessionSecurityEventFactory(DjangoModelFactory):
    """Factory for creating session security events."""
    
    class Meta:
        model = SessionSecurityEvent
    
    user = factory.SubFactory(UserFactory)
    event_type = 'login_success'
    ip_address = factory.Faker('ipv4')
    user_agent = factory.Faker('user_agent')
    timestamp = factory.LazyFunction(timezone.now)
    details = factory.LazyFunction(lambda: {'source': 'test'})


class LoginHistoryFactory(DjangoModelFactory):
    """Factory for creating login history."""
    
    class Meta:
        model = LoginHistory
    
    user = factory.SubFactory(UserFactory)
    ip_address = factory.Faker('ipv4')
    user_agent = factory.Faker('user_agent')
    success = True
    timestamp = factory.LazyFunction(timezone.now)
    failure_reason = None


class ProfilePrivacySettingsFactory(DjangoModelFactory):
    """Factory for creating profile privacy settings."""
    
    class Meta:
        model = ProfilePrivacySettings
    
    user = factory.SubFactory(UserFactory)
    profile_visibility = 'public'
    show_email = False
    show_phone = False
    show_address = False
    allow_contact = True
    marketing_emails = False
    data_processing_consent = True


class TestDataGenerator:
    """Utility class for generating test data."""
    
    @staticmethod
    def create_complete_customer(email: str = None, **kwargs) -> User:
        """Create a complete customer with profile and settings."""
        user = CustomerUserFactory(email=email, **kwargs)
        CustomerProfileFactory(user=user)
        EmailAddressFactory(user=user, email=user.email)
        ProfilePrivacySettingsFactory(user=user)
        return user
    
    @staticmethod
    def create_complete_service_provider(email: str = None, **kwargs) -> User:
        """Create a complete service provider with profile and settings."""
        user = ServiceProviderUserFactory(email=email, **kwargs)
        ServiceProviderProfileFactory(user=user)
        EmailAddressFactory(user=user, email=user.email)
        ProfilePrivacySettingsFactory(user=user)
        return user
    
    @staticmethod
    def create_social_user(provider: str = 'google', **kwargs) -> User:
        """Create a user with social account."""
        user = UserFactory(**kwargs)
        SocialAccountFactory(user=user, provider=provider)
        EmailAddressFactory(user=user, email=user.email)
        return user
    
    @staticmethod
    def create_test_session(user: User = None, **kwargs) -> UserSession:
        """Create a test session for a user."""
        if not user:
            user = UserFactory()
        return UserSessionFactory(user=user, **kwargs)
    
    @staticmethod
    def generate_form_data(form_type: str, role: str = None, **overrides) -> Dict[str, Any]:
        """Generate form data for testing."""
        base_data = {
            'email': f'test{random.randint(1000, 9999)}@example.com',
            'password1': 'TestPass123!',
            'password2': 'TestPass123!',
            'agree_to_terms': True,
        }
        
        if form_type == 'signup':
            if role == User.SERVICE_PROVIDER:
                base_data.update({
                    'role': User.SERVICE_PROVIDER,
                    'business_name': 'Test Business',
                    'contact_name': 'John Doe',
                    'phone': '+**********',
                    'address': '123 Main St',
                    'city': 'Test City',
                    'state': 'CA',
                    'zip_code': '12345',
                })
            else:
                base_data.update({
                    'role': User.CUSTOMER,
                    'first_name': 'John',
                    'last_name': 'Doe',
                })
        
        elif form_type == 'login':
            base_data = {
                'login': base_data['email'],
                'password': base_data['password1'],
            }
        
        base_data.update(overrides)
        return base_data
    
    @staticmethod
    def create_request_factory() -> RequestFactory:
        """Create a request factory with common setup."""
        return RequestFactory()
    
    @staticmethod
    def generate_security_test_data() -> Dict[str, Any]:
        """Generate data for security testing."""
        return {
            'xss_payloads': [
                '<script>alert("xss")</script>',
                '"><script>alert("xss")</script>',
                'javascript:alert("xss")',
                '<img src=x onerror=alert("xss")>',
            ],
            'sql_injection_payloads': [
                "'; DROP TABLE users; --",
                "' OR '1'='1",
                "' UNION SELECT * FROM users --",
                "admin'--",
            ],
            'invalid_emails': [
                'invalid-email',
                '@example.com',
                'test@',
                '<EMAIL>',
                'test@example',
            ],
            'weak_passwords': [
                '123',
                'password',
                '12345678',
                'qwerty',
                'admin',
            ],
            'strong_passwords': [
                'MyStr0ng!Password123',
                'C0mpl3x&Secure#Pass',
                'Ungu3ssable!P@ssw0rd',
            ]
        }
