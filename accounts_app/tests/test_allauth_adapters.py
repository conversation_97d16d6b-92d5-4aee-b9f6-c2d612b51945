# --- Standard Library Imports ---
import os
import sys
from unittest.mock import Mock, patch, MagicMock

# --- Django Setup ---
import django
from django.conf import settings
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
    django.setup()

# --- Django Imports ---
from django.test import TestCase, RequestFactory, override_settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.urls import reverse

# --- Third-party Imports ---
import pytest
from allauth.socialaccount.models import SocialAccount, SocialApp, SocialLogin
from allauth.socialaccount import app_settings
from allauth.account.models import EmailAddress

# --- Local App Imports ---
from accounts_app.allauth_adapters import CozyWishAccountAdapter, CozyWishSocialAccountAdapter
from accounts_app.models import CustomerProfile, ServiceProviderProfile
from accounts_app.forms import CustomerSignupForm, ServiceProviderSignupForm
from .test_utils import ComprehensiveAuthTestCase, MockSocialLoginMixin
from .test_factories import TestDataGenerator

# --- Setup ---
User = get_user_model()


class CozyWishAccountAdapterTests(ComprehensiveAuthTestCase):
    """Comprehensive test cases for CozyWishAccountAdapter."""

    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
        self.adapter = CozyWishAccountAdapter()

    def test_determine_user_role_from_url_customer(self):
        """Test role determination from customer URL."""
        request = self.factory.get('/accounts/customer/signup/')
        form = Mock()
        form.cleaned_data = {}
        # Remove the role attribute from the mock
        if hasattr(form, 'role'):
            del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.CUSTOMER)

    def test_determine_user_role_from_url_provider(self):
        """Test role determination from provider URL."""
        request = self.factory.get('/accounts/provider/signup/')
        form = Mock()
        form.cleaned_data = {}
        # Remove the role attribute from the mock
        del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.SERVICE_PROVIDER)

    def test_determine_user_role_from_form_data(self):
        """Test role determination from form data."""
        request = self.factory.get('/accounts/signup/')
        form = Mock()
        form.cleaned_data = {'role': User.SERVICE_PROVIDER}
        # Remove the role attribute from the mock
        del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.SERVICE_PROVIDER)

    def test_determine_user_role_default(self):
        """Test default role determination."""
        request = self.factory.get('/accounts/signup/')
        form = Mock()
        form.cleaned_data = {}
        # Remove the role attribute from the mock
        del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.CUSTOMER)

    def test_determine_user_role_priority_form_over_url(self):
        """Test that form role takes priority over URL role."""
        request = self.factory.get('/accounts/customer/signup/')
        form = Mock()
        form.cleaned_data = {'role': User.SERVICE_PROVIDER}
        form.role = User.SERVICE_PROVIDER

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.SERVICE_PROVIDER)

    def test_determine_user_role_invalid_url(self):
        """Test role determination with invalid URL."""
        request = self.factory.get('/accounts/invalid/signup/')
        form = Mock()
        form.cleaned_data = {}
        if hasattr(form, 'role'):
            del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.CUSTOMER)  # Should default to customer

    def test_determine_user_role_edge_cases(self):
        """Test role determination edge cases."""
        # Test with None form
        request = self.factory.get('/accounts/signup/')
        role = self.adapter._determine_user_role(request, None)
        self.assertEqual(role, User.CUSTOMER)

        # Test with form without cleaned_data
        form = Mock()
        if hasattr(form, 'cleaned_data'):
            del form.cleaned_data
        if hasattr(form, 'role'):
            del form.role
        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.CUSTOMER)

    def test_email_normalization(self):
        """Test email normalization in clean_email method."""
        test_cases = [
            ('  <EMAIL>  ', '<EMAIL>'),
            ('<EMAIL>', '<EMAIL>'),
            ('<EMAIL>', '<EMAIL>'),
            ('', ''),
            (None, None),
        ]

        for input_email, expected in test_cases:
            with self.subTest(input_email=input_email):
                result = self.adapter.clean_email(input_email)
                self.assertEqual(result, expected)
    
    def test_save_user_customer(self):
        """Test saving customer user with profile creation."""
        request = self.factory.get('/accounts/customer/signup/')
        user = User(email='<EMAIL>')

        # Create a simple form-like object instead of Mock
        class SimpleForm:
            def __init__(self):
                self.cleaned_data = {
                    'email': '<EMAIL>',
                    'first_name': 'John',
                    'last_name': 'Doe',
                    'phone_number': '+**********'
                }

        form = SimpleForm()

        saved_user = self.adapter.save_user(request, user, form, commit=True)

        self.assertEqual(saved_user.role, User.CUSTOMER)
        self.assertTrue(saved_user.is_active)
        self.assertTrue(hasattr(saved_user, 'customer_profile'))
        self.assertEqual(saved_user.customer_profile.first_name, 'John')
        self.assertEqual(saved_user.customer_profile.last_name, 'Doe')

    def test_save_user_service_provider(self):
        """Test saving service provider user with profile creation."""
        request = self.factory.get('/accounts/provider/signup/')
        user = User(email='<EMAIL>', first_name='Jane', last_name='Smith')

        # Create a simple form-like object instead of Mock
        class SimpleForm:
            def __init__(self):
                self.cleaned_data = {
                    'email': '<EMAIL>',
                    'business_name': 'Test Business',
                    'business_phone_number': '+**********',
                    'contact_person_name': 'Jane Smith',
                    'business_address': '123 Test St',
                    'city': 'Test City',
                    'state': 'CA',
                    'zip_code': '12345'
                }

        form = SimpleForm()

        saved_user = self.adapter.save_user(request, user, form, commit=True)

        self.assertEqual(saved_user.role, User.SERVICE_PROVIDER)
        self.assertFalse(saved_user.is_active)  # Requires email verification
        self.assertTrue(hasattr(saved_user, 'service_provider_profile'))
        self.assertEqual(saved_user.service_provider_profile.legal_name, 'Test Business')
        self.assertEqual(saved_user.service_provider_profile.phone, '+**********')

    def test_save_user_without_commit(self):
        """Test saving user without committing to database."""
        request = self.factory.get('/accounts/customer/signup/')
        user = User(email='<EMAIL>')

        class SimpleForm:
            def __init__(self):
                self.cleaned_data = {
                    'email': '<EMAIL>',
                    'first_name': 'John',
                    'last_name': 'Doe',
                }

        form = SimpleForm()
        saved_user = self.adapter.save_user(request, user, form, commit=False)

        # User should not be saved to database
        self.assertIsNone(saved_user.pk)
        self.assertEqual(saved_user.role, User.CUSTOMER)

    def test_save_user_profile_creation_error_handling(self):
        """Test error handling during profile creation."""
        request = self.factory.get('/accounts/customer/signup/')
        user = User(email='<EMAIL>')

        class SimpleForm:
            def __init__(self):
                self.cleaned_data = {
                    'email': '<EMAIL>',
                    # Missing required fields to trigger error
                }

        form = SimpleForm()

        # Should handle profile creation errors gracefully
        with patch('accounts_app.models.CustomerProfile.objects.create') as mock_create:
            mock_create.side_effect = Exception("Profile creation failed")

            # Should not raise exception, but log error
            saved_user = self.adapter.save_user(request, user, form, commit=True)
            self.assertEqual(saved_user.role, User.CUSTOMER)

    def test_save_user_duplicate_profile_handling(self):
        """Test handling of duplicate profile creation."""
        request = self.factory.get('/accounts/customer/signup/')
        user = User.objects.create_user(email='<EMAIL>', password='test123')

        # Create profile first
        CustomerProfile.objects.create(user=user, first_name='John', last_name='Doe')

        class SimpleForm:
            def __init__(self):
                self.cleaned_data = {
                    'email': '<EMAIL>',
                    'first_name': 'Jane',
                    'last_name': 'Smith',
                }

        form = SimpleForm()

        # Should handle existing profile gracefully
        saved_user = self.adapter.save_user(request, user, form, commit=True)
        self.assertEqual(saved_user.role, User.CUSTOMER)

        # Profile should not be duplicated
        profiles = CustomerProfile.objects.filter(user=user)
        self.assertEqual(profiles.count(), 1)
    
    def test_clean_email(self):
        """Test email cleaning functionality."""
        email = '  <EMAIL>  '
        cleaned = self.adapter.clean_email(email)
        self.assertEqual(cleaned, '<EMAIL>')
    
    def test_get_login_redirect_url_customer(self):
        """Test login redirect for customer."""
        user = User.objects.create_user('<EMAIL>', 'password', role=User.CUSTOMER)
        request = self.factory.get('/')
        request.user = user
        
        redirect_url = self.adapter.get_login_redirect_url(request)
        self.assertEqual(redirect_url, '/dashboard/')
    
    def test_get_login_redirect_url_service_provider(self):
        """Test login redirect for service provider."""
        user = User.objects.create_user('<EMAIL>', 'password', role=User.SERVICE_PROVIDER)
        request = self.factory.get('/')
        request.user = user
        
        redirect_url = self.adapter.get_login_redirect_url(request)
        self.assertEqual(redirect_url, '/dashboard/')
    
    def test_get_login_redirect_url_admin(self):
        """Test login redirect for admin."""
        user = User.objects.create_user('<EMAIL>', 'password', role=User.ADMIN)
        request = self.factory.get('/')
        request.user = user
        
        redirect_url = self.adapter.get_login_redirect_url(request)
        self.assertEqual(redirect_url, '/admin-panel/')


class CozyWishSocialAccountAdapterTests(ComprehensiveAuthTestCase, MockSocialLoginMixin):
    """Comprehensive test cases for CozyWishSocialAccountAdapter."""

    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
        self.adapter = CozyWishSocialAccountAdapter()

        # Create social app for testing
        self.social_app = SocialApp.objects.create(
            provider='google',
            name='Google',
            client_id='test_client_id',
            secret='test_secret'
        )
    
    def test_determine_social_user_role_default(self):
        """Test default social user role determination."""
        request = self.factory.get('/accounts/social/signup/')
        sociallogin = Mock()
        data = {'email': '<EMAIL>', 'name': 'John Doe'}
        
        role = self.adapter._determine_social_user_role(request, sociallogin, data)
        self.assertEqual(role, User.CUSTOMER)
    
    def test_determine_social_user_role_business_url(self):
        """Test social user role determination from business URL."""
        request = self.factory.get('/accounts/provider/social/signup/')
        sociallogin = Mock()
        data = {'email': '<EMAIL>', 'name': 'John Doe'}
        
        role = self.adapter._determine_social_user_role(request, sociallogin, data)
        self.assertEqual(role, User.SERVICE_PROVIDER)
    
    def test_is_business_account_by_name(self):
        """Test business account detection by name."""
        sociallogin = Mock()
        data = {'name': 'ABC Business Services', 'email': '<EMAIL>'}
        
        is_business = self.adapter._is_business_account(sociallogin, data)
        self.assertTrue(is_business)
    
    def test_is_business_account_by_email_domain(self):
        """Test business account detection by email domain."""
        sociallogin = self.create_mock_sociallogin()
        data = {'name': 'John Doe', 'email': '<EMAIL>'}

        is_business = self.adapter._is_business_account(sociallogin, data)
        self.assertTrue(is_business)

    def test_is_business_account_by_name_patterns(self):
        """Test business account detection by name patterns."""
        test_cases = [
            ('ABC Corp', True),
            ('XYZ LLC', True),
            ('Test Inc.', True),
            ('Business Solutions Ltd', True),
            ('John Doe', False),
            ('Jane Smith', False),
            ('Personal Account', False),
        ]

        for name, expected in test_cases:
            with self.subTest(name=name):
                sociallogin = self.create_mock_sociallogin()
                data = {'name': name, 'email': '<EMAIL>'}

                is_business = self.adapter._is_business_account(sociallogin, data)
                self.assertEqual(is_business, expected)

    def test_is_business_account_by_url_context(self):
        """Test business account detection by URL context."""
        # Test provider signup URL
        sociallogin = self.create_mock_sociallogin()
        sociallogin.state = {'next': '/accounts/provider/signup/'}
        data = {'name': 'John Doe', 'email': '<EMAIL>'}

        is_business = self.adapter._is_business_account(sociallogin, data)
        self.assertTrue(is_business)

        # Test customer signup URL
        sociallogin.state = {'next': '/accounts/customer/signup/'}
        is_business = self.adapter._is_business_account(sociallogin, data)
        self.assertFalse(is_business)

    def test_is_business_account_edge_cases(self):
        """Test business account detection edge cases."""
        # Test with missing data
        sociallogin = self.create_mock_sociallogin()

        # No name or email
        is_business = self.adapter._is_business_account(sociallogin, {})
        self.assertFalse(is_business)

        # Only email
        is_business = self.adapter._is_business_account(sociallogin, {'email': '<EMAIL>'})
        self.assertFalse(is_business)

        # Only name
        is_business = self.adapter._is_business_account(sociallogin, {'name': 'John Doe'})
        self.assertFalse(is_business)

    def test_extract_user_data_google(self):
        """Test user data extraction from Google social login."""
        sociallogin = self.create_mock_sociallogin('google', {
            'email': '<EMAIL>',
            'given_name': 'John',
            'family_name': 'Doe',
            'picture': 'https://example.com/avatar.jpg',
            'verified_email': True
        })

        data = self.adapter._extract_user_data(sociallogin)

        expected = {
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
            'name': 'John Doe',
        }

        for key, value in expected.items():
            self.assertEqual(data.get(key), value)

    def test_extract_user_data_facebook(self):
        """Test user data extraction from Facebook social login."""
        sociallogin = self.create_mock_sociallogin('facebook', {
            'email': '<EMAIL>',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'name': 'Jane Smith',
            'picture': {'data': {'url': 'https://example.com/avatar.jpg'}}
        })

        data = self.adapter._extract_user_data(sociallogin)

        expected = {
            'email': '<EMAIL>',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'name': 'Jane Smith',
        }

        for key, value in expected.items():
            self.assertEqual(data.get(key), value)

    def test_extract_user_data_missing_fields(self):
        """Test user data extraction with missing fields."""
        sociallogin = self.create_mock_sociallogin('google', {
            'email': '<EMAIL>',
            # Missing name fields
        })

        data = self.adapter._extract_user_data(sociallogin)

        self.assertEqual(data.get('email'), '<EMAIL>')
        self.assertEqual(data.get('first_name'), '')
        self.assertEqual(data.get('last_name'), '')
        self.assertEqual(data.get('name'), '')

    def test_save_user_social_customer(self):
        """Test saving social customer user."""
        request = self.factory.get('/accounts/social/signup/')
        user = User(email='<EMAIL>')
        sociallogin = self.create_mock_sociallogin()

        saved_user = self.adapter.save_user(request, user, sociallogin)

        self.assertEqual(saved_user.role, User.CUSTOMER)
        self.assertTrue(saved_user.is_active)
        self.assertTrue(hasattr(saved_user, 'customer_profile'))

    def test_save_user_social_business(self):
        """Test saving social business user."""
        request = self.factory.get('/accounts/social/signup/')
        user = User(email='<EMAIL>')
        sociallogin = self.create_mock_sociallogin('google', {
            'email': '<EMAIL>',
            'name': 'Business Corp',
            'given_name': 'Business',
            'family_name': 'Corp'
        })

        saved_user = self.adapter.save_user(request, user, sociallogin)

        self.assertEqual(saved_user.role, User.SERVICE_PROVIDER)
        self.assertFalse(saved_user.is_active)  # Requires verification
        self.assertTrue(hasattr(saved_user, 'service_provider_profile'))
    
    def test_is_business_account_personal(self):
        """Test personal account detection."""
        sociallogin = Mock()
        data = {'name': 'John Doe', 'email': '<EMAIL>'}
        
        is_business = self.adapter._is_business_account(sociallogin, data)
        self.assertFalse(is_business)
    
    def test_extract_social_user_data(self):
        """Test extracting user data from social providers."""
        user = User(email='<EMAIL>')
        sociallogin = Mock()
        data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'name': 'John Doe'
        }
        
        self.adapter._extract_social_user_data(user, sociallogin, data)
        
        self.assertEqual(user.first_name, 'John')
        self.assertEqual(user.last_name, 'Doe')
    
    def test_extract_social_user_data_full_name_only(self):
        """Test extracting user data when only full name is available."""
        user = User(email='<EMAIL>')
        sociallogin = Mock()
        data = {'name': 'John Michael Doe'}
        
        self.adapter._extract_social_user_data(user, sociallogin, data)
        
        self.assertEqual(user.first_name, 'John')
        self.assertEqual(user.last_name, 'Michael Doe')
    
    @patch('accounts_app.allauth_adapters.CustomerProfile.objects.create')
    def test_create_social_customer_profile(self, mock_create):
        """Test creating customer profile from social data."""
        user = User(email='<EMAIL>', first_name='John', last_name='Doe')
        extra_data = {'gender': 'male'}
        
        mock_profile = Mock()
        mock_create.return_value = mock_profile
        
        profile = self.adapter._create_social_customer_profile(user, extra_data)
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args
        self.assertEqual(call_args[1]['first_name'], 'John')
        self.assertEqual(call_args[1]['last_name'], 'Doe')
        self.assertEqual(call_args[1]['gender'], 'M')
    
    @patch('accounts_app.allauth_adapters.ServiceProviderProfile.objects.create')
    def test_create_social_service_provider_profile(self, mock_create):
        """Test creating service provider profile from social data."""
        user = User(email='<EMAIL>', first_name='Jane', last_name='Smith')
        extra_data = {'company': 'Test Company', 'website': 'https://testcompany.com'}
        
        mock_profile = Mock()
        mock_create.return_value = mock_profile
        
        profile = self.adapter._create_social_service_provider_profile(user, extra_data)
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args
        self.assertEqual(call_args[1]['legal_name'], 'Test Company')
        self.assertEqual(call_args[1]['website'], 'https://testcompany.com')
        self.assertEqual(call_args[1]['contact_name'], 'Jane Smith')

    def test_social_login_security_validation(self):
        """Test security validation in social login."""
        request = self.factory.get('/accounts/social/signup/')

        # Test with malicious data
        malicious_data = {
            'email': '<script>alert("xss")</script>@example.com',
            'name': '<script>alert("xss")</script>',
            'given_name': '"><script>alert("xss")</script>',
            'family_name': 'javascript:alert("xss")'
        }

        sociallogin = self.create_mock_sociallogin('google', malicious_data)
        user = User(email='<EMAIL>')

        # Should sanitize malicious input
        saved_user = self.adapter.save_user(request, user, sociallogin)
        self.assertNotIn('<script>', saved_user.first_name)
        self.assertNotIn('<script>', saved_user.last_name)

    def test_social_login_rate_limiting_integration(self):
        """Test integration with rate limiting for social login."""
        request = self.factory.get('/accounts/social/signup/')

        # Simulate multiple rapid social login attempts
        for i in range(3):
            user = User(email=f'user{i}@example.com')
            sociallogin = self.create_mock_sociallogin()

            # Should not be blocked by rate limiting for different users
            saved_user = self.adapter.save_user(request, user, sociallogin)
            self.assertIsNotNone(saved_user)

    def test_adapter_error_logging(self):
        """Test that adapter errors are properly logged."""
        request = self.factory.get('/accounts/signup/')
        user = User(email='<EMAIL>')

        class ErrorForm:
            def __init__(self):
                self.cleaned_data = {'email': '<EMAIL>'}

        form = ErrorForm()

        with patch('accounts_app.allauth_adapters.logger') as mock_logger:
            with patch('accounts_app.models.CustomerProfile.objects.create') as mock_create:
                mock_create.side_effect = Exception("Database error")

                # Should log the error
                self.adapter.save_user(request, user, form, commit=True)
                mock_logger.error.assert_called()

    def test_adapter_performance_optimization(self):
        """Test adapter performance optimizations."""
        request = self.factory.get('/accounts/customer/signup/')
        user = User(email='<EMAIL>')

        class SimpleForm:
            def __init__(self):
                self.cleaned_data = {
                    'email': '<EMAIL>',
                    'first_name': 'John',
                    'last_name': 'Doe',
                }

        form = SimpleForm()

        # Test with database query optimization
        with self.assert_max_queries(5):  # Should not exceed 5 queries
            saved_user = self.adapter.save_user(request, user, form, commit=True)
            self.assertIsNotNone(saved_user)

    def test_adapter_accessibility_compliance(self):
        """Test that adapter maintains accessibility compliance."""
        # This would test that any error messages or redirects
        # maintain accessibility standards
        request = self.factory.get('/accounts/signup/')

        # Test error handling maintains accessibility
        with patch('accounts_app.allauth_adapters.messages') as mock_messages:
            user = User(email='invalid-email')

            class ErrorForm:
                def __init__(self):
                    self.cleaned_data = {'email': 'invalid-email'}

            form = ErrorForm()

            # Should provide accessible error messages
            try:
                self.adapter.save_user(request, user, form, commit=True)
            except Exception:
                pass  # Expected for invalid data

            # Verify error messages are accessible
            if mock_messages.error.called:
                error_message = mock_messages.error.call_args[0][1]
                self.assertIsInstance(error_message, str)
                self.assertGreater(len(error_message), 0)
