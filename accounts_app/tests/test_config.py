# --- Standard Library Imports ---
import os
import tempfile
from unittest.mock import patch

# --- Django Imports ---
from django.test import override_settings
from django.core.cache import cache
from django.core import mail

# --- Test Configuration ---

# Test database settings
TEST_DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        },
        'TEST': {
            'NAME': ':memory:',
        }
    }
}

# Test cache settings
TEST_CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'test-cache',
    }
}

# Test email settings
TEST_EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# Test media settings
TEST_MEDIA_ROOT = tempfile.mkdtemp()

# Test static files settings
TEST_STATIC_ROOT = tempfile.mkdtemp()

# Test security settings
TEST_SECRET_KEY = 'test-secret-key-for-testing-only-not-for-production'

# Test allauth settings
TEST_ALLAUTH_SETTINGS = {
    'ACCOUNT_EMAIL_VERIFICATION': 'mandatory',
    'ACCOUNT_EMAIL_REQUIRED': True,
    'ACCOUNT_USERNAME_REQUIRED': False,
    'ACCOUNT_AUTHENTICATION_METHOD': 'email',
    'ACCOUNT_UNIQUE_EMAIL': True,
    'ACCOUNT_LOGIN_REDIRECT_URL': '/dashboard/',
    'ACCOUNT_SIGNUP_REDIRECT_URL': '/dashboard/',
    'ACCOUNT_LOGOUT_REDIRECT_URL': '/',
    'ACCOUNT_EMAIL_SUBJECT_PREFIX': '[CozyWish Test] ',
    'ACCOUNT_DEFAULT_HTTP_PROTOCOL': 'http',  # Use HTTP for testing
    'ACCOUNT_RATE_LIMITS': {
        'login_failed': '10/5m',  # More lenient for testing
    },
    'SOCIALACCOUNT_AUTO_SIGNUP': False,
    'SOCIALACCOUNT_EMAIL_VERIFICATION': 'none',  # Simplified for testing
}

# Test logging settings
TEST_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'loggers': {
        'accounts_app': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django.security': {
            'handlers': ['null'],  # Suppress security warnings in tests
            'level': 'ERROR',
            'propagate': False,
        },
    },
}

# Test middleware settings
TEST_MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # Add custom middleware for testing
    'accounts_app.middleware.security_enhancements.SecurityEnhancementMiddleware',
    'accounts_app.middleware.session_management.SessionManagementMiddleware',
]

# Test password validators (simplified for testing)
TEST_AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


class TestConfigMixin:
    """Mixin to provide test configuration utilities."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test class configuration."""
        super().setUpClass()
        
        # Clear cache
        cache.clear()
        
        # Clear mail outbox
        mail.outbox = []
    
    def setUp(self):
        """Set up test configuration."""
        super().setUp()
        
        # Clear cache before each test
        cache.clear()
        
        # Clear mail outbox before each test
        mail.outbox = []
        
        # Reset any global state
        self._reset_global_state()
    
    def tearDown(self):
        """Clean up after test."""
        super().tearDown()
        
        # Clear cache after each test
        cache.clear()
        
        # Clear mail outbox after each test
        mail.outbox = []
        
        # Clean up any test data
        self._cleanup_test_data()
    
    def _reset_global_state(self):
        """Reset any global state that might affect tests."""
        # Reset rate limiting cache
        from django.core.cache import cache
        cache.clear()
        
        # Reset any singleton instances
        # This would depend on specific implementation
    
    def _cleanup_test_data(self):
        """Clean up test data."""
        # Clean up any temporary files
        # Clean up any test database records
        # This would depend on specific test requirements
        pass


def get_test_settings():
    """Get test-specific Django settings."""
    return {
        'DATABASES': TEST_DATABASES,
        'CACHES': TEST_CACHES,
        'EMAIL_BACKEND': TEST_EMAIL_BACKEND,
        'MEDIA_ROOT': TEST_MEDIA_ROOT,
        'STATIC_ROOT': TEST_STATIC_ROOT,
        'SECRET_KEY': TEST_SECRET_KEY,
        'LOGGING': TEST_LOGGING,
        'MIDDLEWARE': TEST_MIDDLEWARE,
        'AUTH_PASSWORD_VALIDATORS': TEST_AUTH_PASSWORD_VALIDATORS,
        **TEST_ALLAUTH_SETTINGS,
        
        # Test-specific settings
        'DEBUG': False,  # Test with DEBUG=False unless specifically testing debug mode
        'TESTING': True,
        'PASSWORD_HASHERS': [
            'django.contrib.auth.hashers.MD5PasswordHasher',  # Fast for testing
        ],
        'USE_TZ': True,
        'TIME_ZONE': 'UTC',
        
        # Disable migrations for faster testing
        'MIGRATION_MODULES': {
            'accounts_app': None,
            'auth': None,
            'contenttypes': None,
            'sessions': None,
        },
        
        # Security settings for testing
        'SECURE_SSL_REDIRECT': False,
        'SECURE_HSTS_SECONDS': 0,
        'SECURE_CONTENT_TYPE_NOSNIFF': True,
        'SECURE_BROWSER_XSS_FILTER': True,
        'X_FRAME_OPTIONS': 'DENY',
        
        # Session settings for testing
        'SESSION_COOKIE_SECURE': False,  # Allow HTTP for testing
        'SESSION_COOKIE_HTTPONLY': True,
        'SESSION_COOKIE_AGE': 3600,  # 1 hour for testing
        'SESSION_EXPIRE_AT_BROWSER_CLOSE': False,
        
        # CSRF settings for testing
        'CSRF_COOKIE_SECURE': False,  # Allow HTTP for testing
        'CSRF_COOKIE_HTTPONLY': True,
        'CSRF_USE_SESSIONS': False,
        
        # Rate limiting settings for testing
        'RATELIMIT_ENABLE': True,
        'RATELIMIT_USE_CACHE': 'default',
        
        # Custom app settings for testing
        'ACCOUNTS_APP_TESTING': True,
        'ACCOUNTS_APP_ENABLE_SECURITY_LOGGING': True,
        'ACCOUNTS_APP_ENABLE_SESSION_MANAGEMENT': True,
        'ACCOUNTS_APP_ENABLE_RATE_LIMITING': True,
    }


def override_test_settings(**kwargs):
    """Decorator to override settings for specific tests."""
    test_settings = get_test_settings()
    test_settings.update(kwargs)
    return override_settings(**test_settings)


class MockEmailBackend:
    """Mock email backend for testing."""
    
    def __init__(self):
        self.messages = []
    
    def send_messages(self, email_messages):
        """Mock send_messages method."""
        self.messages.extend(email_messages)
        return len(email_messages)
    
    def clear(self):
        """Clear sent messages."""
        self.messages = []


class MockCacheBackend:
    """Mock cache backend for testing."""
    
    def __init__(self):
        self._cache = {}
    
    def get(self, key, default=None):
        """Mock get method."""
        return self._cache.get(key, default)
    
    def set(self, key, value, timeout=None):
        """Mock set method."""
        self._cache[key] = value
    
    def delete(self, key):
        """Mock delete method."""
        self._cache.pop(key, None)
    
    def clear(self):
        """Mock clear method."""
        self._cache.clear()


# Test fixtures and data
TEST_USER_DATA = {
    'customer': {
        'email': '<EMAIL>',
        'password': 'TestPass123!',
        'first_name': 'John',
        'last_name': 'Doe',
        'role': 'customer',
    },
    'provider': {
        'email': '<EMAIL>',
        'password': 'TestPass123!',
        'business_name': 'Test Business',
        'contact_name': 'Jane Smith',
        'phone': '+**********',
        'role': 'service_provider',
    },
    'admin': {
        'email': '<EMAIL>',
        'password': 'TestPass123!',
        'first_name': 'Admin',
        'last_name': 'User',
        'role': 'admin',
        'is_staff': True,
        'is_superuser': True,
    }
}

# Test URLs for different scenarios
TEST_URLS = {
    'login': '/accounts/login/',
    'logout': '/accounts/logout/',
    'signup': '/accounts/signup/',
    'customer_signup': '/accounts/customer/signup/',
    'provider_signup': '/accounts/provider/signup/',
    'password_reset': '/accounts/password/reset/',
    'dashboard': '/dashboard/',
    'profile': '/accounts/profile/',
}

# Test form data templates
TEST_FORM_DATA = {
    'valid_customer_signup': {
        'email': '<EMAIL>',
        'password1': 'TestPass123!',
        'password2': 'TestPass123!',
        'first_name': 'New',
        'last_name': 'Customer',
        'role': 'customer',
        'agree_to_terms': True,
    },
    'valid_provider_signup': {
        'email': '<EMAIL>',
        'password1': 'TestPass123!',
        'password2': 'TestPass123!',
        'business_name': 'New Business',
        'contact_name': 'Business Owner',
        'phone': '+**********',
        'address': '123 Business St',
        'city': 'Business City',
        'state': 'CA',
        'zip_code': '12345',
        'role': 'service_provider',
        'agree_to_terms': True,
    },
    'valid_login': {
        'login': '<EMAIL>',
        'password': 'TestPass123!',
    },
    'invalid_login': {
        'login': '<EMAIL>',
        'password': 'wrongpassword',
    },
}

# Test constants
TEST_CONSTANTS = {
    'MAX_LOGIN_ATTEMPTS': 5,
    'RATE_LIMIT_WINDOW': 300,  # 5 minutes
    'SESSION_TIMEOUT': 3600,   # 1 hour
    'PASSWORD_MIN_LENGTH': 8,
    'EMAIL_VERIFICATION_TIMEOUT': 86400,  # 24 hours
}
