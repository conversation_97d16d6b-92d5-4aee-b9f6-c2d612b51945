# --- Standard Library Imports ---
import re
from bs4 import BeautifulSoup

# --- Django Imports ---
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# --- Local App Imports ---
from .test_utils import ComprehensiveAuthTestCase, AccessibilityTestMixin
from .test_factories import TestDataGenerator

User = get_user_model()


class AuthenticationAccessibilityTests(ComprehensiveAuthTestCase, AccessibilityTestMixin):
    """Accessibility tests for authentication system."""
    
    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
    
    def test_login_form_accessibility(self):
        """Test login form accessibility compliance."""
        response = self.client.get(reverse('account_login'))
        self.assertEqual(response.status_code, 200)
        
        # Test general form accessibility
        self.assert_form_accessibility(response)
        
        # Parse HTML for detailed testing
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Test form labels
        email_input = soup.find('input', {'name': 'login'}) or soup.find('input', {'type': 'email'})
        if email_input:
            label = soup.find('label', {'for': email_input.get('id')})
            self.assertIsNotNone(label, "Email input should have associated label")
        
        password_input = soup.find('input', {'type': 'password'})
        if password_input:
            label = soup.find('label', {'for': password_input.get('id')})
            self.assertIsNotNone(label, "Password input should have associated label")
        
        # Test ARIA attributes
        form = soup.find('form')
        if form:
            # Check for ARIA landmarks
            self.assertTrue(
                form.get('role') or form.get('aria-label') or form.get('aria-labelledby'),
                "Form should have ARIA landmark or label"
            )
        
        # Test keyboard navigation
        self.assert_keyboard_navigation(response)
        
        # Test screen reader support
        self.assert_screen_reader_support(response)
    
    def test_signup_form_accessibility(self):
        """Test signup form accessibility compliance."""
        urls = [
            reverse('accounts_app:customer_signup'),
            reverse('accounts_app:provider_signup'),
        ]
        
        for url in urls:
            with self.subTest(url=url):
                response = self.client.get(url)
                self.assertEqual(response.status_code, 200)
                
                # Test general form accessibility
                self.assert_form_accessibility(response)
                
                # Parse HTML for detailed testing
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Test required field indicators
                required_inputs = soup.find_all('input', {'required': True})
                for input_field in required_inputs:
                    # Should have aria-required or visual indicator
                    self.assertTrue(
                        input_field.get('aria-required') == 'true' or
                        input_field.get('required') is not None,
                        f"Required field {input_field.get('name')} should have proper indicators"
                    )
                
                # Test error handling accessibility
                self.assert_keyboard_navigation(response)
                self.assert_screen_reader_support(response)
    
    def test_form_error_accessibility(self):
        """Test form error accessibility."""
        # Submit invalid signup form to trigger errors
        signup_data = {
            'email': 'invalid-email',
            'password1': '123',  # Weak password
            'password2': '456',  # Mismatched password
            'agree_to_terms': False,
        }
        
        response = self.client.post(reverse('accounts_app:customer_signup'), signup_data)
        
        if response.status_code == 200:  # Form has errors
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Test error message accessibility
            error_elements = soup.find_all(class_=re.compile(r'error|invalid'))
            for error in error_elements:
                # Error messages should be associated with fields
                self.assertTrue(
                    error.get('id') or error.get('aria-describedby') or 
                    error.get('role') == 'alert',
                    "Error messages should have proper ARIA attributes"
                )
            
            # Test field error associations
            invalid_inputs = soup.find_all('input', {'aria-invalid': 'true'})
            for input_field in invalid_inputs:
                # Should be described by error message
                describedby = input_field.get('aria-describedby')
                if describedby:
                    error_element = soup.find(id=describedby)
                    self.assertIsNotNone(error_element, 
                                       f"Field {input_field.get('name')} should have accessible error description")
    
    def test_password_field_accessibility(self):
        """Test password field accessibility features."""
        response = self.client.get(reverse('accounts_app:customer_signup'))
        soup = BeautifulSoup(response.content, 'html.parser')
        
        password_inputs = soup.find_all('input', {'type': 'password'})
        
        for password_input in password_inputs:
            # Test password field attributes
            self.assertTrue(
                password_input.get('autocomplete') in ['new-password', 'current-password'] or
                password_input.get('autocomplete') is None,
                "Password fields should have appropriate autocomplete attributes"
            )
            
            # Test password visibility toggle if present
            toggle_button = soup.find('button', {'aria-controls': password_input.get('id')})
            if toggle_button:
                self.assertIsNotNone(toggle_button.get('aria-label'),
                                   "Password toggle should have aria-label")
    
    def test_form_navigation_accessibility(self):
        """Test form navigation accessibility."""
        response = self.client.get(reverse('accounts_app:provider_signup'))
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Test tab order
        focusable_elements = soup.find_all(['input', 'button', 'select', 'textarea', 'a'])
        tabindex_elements = [el for el in focusable_elements if el.get('tabindex')]
        
        # Tabindex values should be logical
        for element in tabindex_elements:
            tabindex = element.get('tabindex')
            if tabindex and tabindex != '-1':
                try:
                    tabindex_value = int(tabindex)
                    self.assertGreaterEqual(tabindex_value, 0, 
                                          "Tabindex should be non-negative or -1")
                except ValueError:
                    self.fail(f"Invalid tabindex value: {tabindex}")
        
        # Test fieldset and legend for grouped fields
        fieldsets = soup.find_all('fieldset')
        for fieldset in fieldsets:
            legend = fieldset.find('legend')
            self.assertIsNotNone(legend, "Fieldsets should have legends")
    
    def test_color_contrast_compliance(self):
        """Test color contrast compliance."""
        response = self.client.get(reverse('account_login'))
        content = response.content.decode()
        
        # Test that important elements don't rely solely on color
        # This is a basic test - full color contrast testing requires specialized tools
        
        # Check for CSS classes that might indicate color-only communication
        problematic_patterns = [
            r'color:\s*red[^;]*;[^}]*(?!.*background)',  # Red text without background
            r'color:\s*green[^;]*;[^}]*(?!.*background)',  # Green text without background
        ]
        
        for pattern in problematic_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                # Should also have other indicators (icons, text, etc.)
                self.assertTrue(
                    'icon' in content.lower() or 'symbol' in content.lower(),
                    "Color-only indicators should have additional visual cues"
                )
    
    def test_responsive_accessibility(self):
        """Test accessibility in responsive design."""
        # Test with different viewport sizes
        viewports = [
            {'width': 320, 'height': 568},   # Mobile
            {'width': 768, 'height': 1024},  # Tablet
            {'width': 1920, 'height': 1080}, # Desktop
        ]
        
        for viewport in viewports:
            with self.subTest(viewport=viewport):
                # Simulate different viewport (basic test)
                response = self.client.get(reverse('account_login'))
                self.assertEqual(response.status_code, 200)
                
                # Test that essential accessibility features are maintained
                self.assert_form_accessibility(response)
                self.assert_keyboard_navigation(response)
    
    def test_internationalization_accessibility(self):
        """Test accessibility with internationalization."""
        # Test with different language settings
        from django.utils import translation
        
        languages = ['en', 'es', 'fr']  # Test available languages
        
        for lang_code in languages:
            with self.subTest(language=lang_code):
                with translation.override(lang_code):
                    response = self.client.get(reverse('account_login'))
                    
                    if response.status_code == 200:
                        # Test that accessibility is maintained across languages
                        self.assert_form_accessibility(response)
                        
                        # Test language attributes
                        soup = BeautifulSoup(response.content, 'html.parser')
                        html_element = soup.find('html')
                        if html_element:
                            lang_attr = html_element.get('lang')
                            self.assertIsNotNone(lang_attr, 
                                               "HTML element should have lang attribute")
    
    def test_focus_management(self):
        """Test focus management in authentication flows."""
        # Test login page focus
        response = self.client.get(reverse('account_login'))
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # First focusable element should be logical (usually email field)
        first_input = soup.find('input')
        if first_input:
            # Should not have negative tabindex unless intentional
            tabindex = first_input.get('tabindex')
            if tabindex:
                self.assertNotEqual(tabindex, '-1', 
                                  "First input should be focusable")
        
        # Test focus indicators
        # This would require JavaScript testing in a real browser
        # Here we test for CSS focus styles
        content = response.content.decode()
        focus_styles = re.findall(r':focus[^{]*{[^}]*}', content, re.IGNORECASE)
        self.assertGreater(len(focus_styles), 0, 
                         "Should have CSS focus styles defined")
    
    def test_skip_navigation_links(self):
        """Test skip navigation links."""
        response = self.client.get(reverse('account_login'))
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for skip links
        skip_links = soup.find_all('a', href=re.compile(r'^#'))
        skip_to_content = [link for link in skip_links 
                          if 'skip' in link.get_text().lower() or 
                             'content' in link.get_text().lower()]
        
        if skip_to_content:
            # Skip links should be properly implemented
            for link in skip_to_content:
                href = link.get('href')
                target = soup.find(id=href[1:])  # Remove # from href
                self.assertIsNotNone(target, 
                                   f"Skip link target {href} should exist")
    
    def test_form_instructions_accessibility(self):
        """Test form instructions accessibility."""
        response = self.client.get(reverse('accounts_app:provider_signup'))
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Test help text accessibility
        help_texts = soup.find_all(class_=re.compile(r'help|hint|instruction'))
        for help_text in help_texts:
            # Help text should be associated with form fields
            help_id = help_text.get('id')
            if help_id:
                associated_input = soup.find('input', {'aria-describedby': help_id})
                self.assertIsNotNone(associated_input, 
                                   "Help text should be associated with input field")
        
        # Test required field instructions
        required_text = soup.find(text=re.compile(r'required|mandatory|\*', re.IGNORECASE))
        if required_text:
            # Should be programmatically associated or clearly indicated
            pass  # Basic test - more detailed testing would require specific implementation
    
    def test_error_prevention_accessibility(self):
        """Test error prevention accessibility features."""
        response = self.client.get(reverse('accounts_app:customer_signup'))
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Test input validation attributes
        email_input = soup.find('input', {'type': 'email'})
        if email_input:
            # Should have proper input type for validation
            self.assertEqual(email_input.get('type'), 'email')
            
            # May have pattern or other validation attributes
            pattern = email_input.get('pattern')
            if pattern:
                # Pattern should be valid regex
                try:
                    re.compile(pattern)
                except re.error:
                    self.fail(f"Invalid regex pattern: {pattern}")
        
        # Test password strength indicators
        password_inputs = soup.find_all('input', {'type': 'password'})
        for password_input in password_inputs:
            # May have associated strength meter
            strength_meter = soup.find(attrs={'aria-describedby': password_input.get('id')})
            if strength_meter:
                # Strength meter should have proper ARIA attributes
                self.assertTrue(
                    strength_meter.get('role') or strength_meter.get('aria-label'),
                    "Password strength meter should have ARIA attributes"
                )
