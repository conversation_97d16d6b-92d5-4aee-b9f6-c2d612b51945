# --- Standard Library Imports ---
import time
from unittest.mock import patch, Mock

# --- Django Imports ---
from django.test import TestCase, Client, override_settings
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core.cache import cache
from django.middleware.csrf import get_token
from django.contrib.sessions.models import Session

# --- Local App Imports ---
from .test_utils import ComprehensiveAuthTestCase, SecurityTestMixin
from .test_factories import TestDataGenerator
from ..models import UserSession, SessionSecurityEvent, LoginHistory

User = get_user_model()


class AuthenticationSecurityTests(ComprehensiveAuthTestCase, SecurityTestMixin):
    """Comprehensive security tests for authentication system."""
    
    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
        cache.clear()
    
    def test_csrf_protection_on_authentication_forms(self):
        """Test CSRF protection on all authentication forms."""
        auth_urls = [
            reverse('account_login'),
            reverse('account_signup'),
            reverse('account_reset_password'),
            reverse('accounts_app:customer_signup'),
            reverse('accounts_app:provider_signup'),
        ]
        
        for url in auth_urls:
            with self.subTest(url=url):
                self.test_csrf_protection(url, 'POST', {'email': '<EMAIL>'})
    
    def test_rate_limiting_on_login_attempts(self):
        """Test rate limiting on login attempts."""
        login_url = reverse('account_login')
        login_data = {
            'login': '<EMAIL>',
            'password': 'wrongpassword',
        }
        
        # Clear cache to reset rate limits
        cache.clear()
        
        # Make multiple failed login attempts
        for i in range(6):  # Exceed typical limit of 5
            response = self.client.post(login_url, login_data)
            
            if i < 5:
                # Should not be rate limited yet
                self.assertNotEqual(response.status_code, 429)
            else:
                # Should be rate limited now or show error
                self.assertIn(response.status_code, [429, 200])
                if response.status_code == 200:
                    # Check for rate limit message in content
                    content = response.content.decode().lower()
                    self.assertTrue(
                        'too many' in content or 
                        'rate limit' in content or
                        'try again' in content
                    )
    
    def test_rate_limiting_on_signup_attempts(self):
        """Test rate limiting on signup attempts."""
        signup_url = reverse('account_signup')
        
        # Clear cache
        cache.clear()
        
        # Make multiple signup attempts
        for i in range(6):
            signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER, {
                'email': f'test{i}@example.com'
            })
            
            response = self.client.post(signup_url, signup_data)
            
            # Rate limiting behavior may vary
            self.assertIn(response.status_code, [200, 302, 429])
    
    def test_rate_limiting_on_password_reset(self):
        """Test rate limiting on password reset attempts."""
        reset_url = reverse('account_reset_password')
        
        # Clear cache
        cache.clear()
        
        # Make multiple password reset attempts
        for i in range(6):
            reset_data = {'email': f'test{i}@example.com'}
            response = self.client.post(reset_url, reset_data)
            
            # Should handle rate limiting gracefully
            self.assertIn(response.status_code, [200, 302, 429])
    
    def test_xss_protection_in_authentication_forms(self):
        """Test XSS protection in authentication forms."""
        # Test login form
        login_url = reverse('account_login')
        self.test_xss_protection(login_url, ['login', 'password'])
        
        # Test signup form
        signup_url = reverse('accounts_app:customer_signup')
        self.test_xss_protection(signup_url, ['email', 'first_name', 'last_name'])
        
        # Test provider signup form
        provider_url = reverse('accounts_app:provider_signup')
        self.test_xss_protection(provider_url, ['email', 'business_name', 'contact_name'])
    
    def test_sql_injection_protection(self):
        """Test SQL injection protection in authentication forms."""
        # Test login form
        login_url = reverse('account_login')
        self.test_sql_injection_protection(login_url, ['login', 'password'])
        
        # Test signup forms
        signup_url = reverse('accounts_app:customer_signup')
        self.test_sql_injection_protection(signup_url, ['email', 'first_name', 'last_name'])
    
    def test_session_fixation_prevention(self):
        """Test session fixation prevention."""
        # Step 1: Get initial session
        response = self.client.get(reverse('account_login'))
        initial_session_key = self.client.session.session_key
        
        # Step 2: Login
        login_data = {
            'login': self.customer_user.email,
            'password': 'TestPass123!',
        }
        response = self.client.post(reverse('account_login'), login_data)
        
        # Step 3: Check that session key changed after login
        new_session_key = self.client.session.session_key
        self.assertNotEqual(initial_session_key, new_session_key)
    
    def test_session_security_headers(self):
        """Test that security headers are properly set."""
        response = self.client.get(reverse('account_login'))
        
        # Check for security headers
        security_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
        ]
        
        for header in security_headers:
            self.assertIn(header, response, f"Missing security header: {header}")
    
    def test_password_security_validation(self):
        """Test password security validation."""
        weak_passwords = self.test_data.generate_security_test_data()['weak_passwords']
        
        for password in weak_passwords:
            with self.subTest(password=password):
                signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER)
                signup_data['password1'] = password
                signup_data['password2'] = password
                
                response = self.client.post(reverse('accounts_app:customer_signup'), signup_data)
                
                # Should reject weak passwords
                if response.status_code == 200:
                    # Check for password validation errors
                    content = response.content.decode().lower()
                    self.assertTrue(
                        'password' in content and 'error' in content,
                        f"Weak password '{password}' was not rejected"
                    )
    
    def test_email_validation_security(self):
        """Test email validation security."""
        invalid_emails = self.test_data.generate_security_test_data()['invalid_emails']
        
        for email in invalid_emails:
            with self.subTest(email=email):
                signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER)
                signup_data['email'] = email
                
                response = self.client.post(reverse('accounts_app:customer_signup'), signup_data)
                
                # Should reject invalid emails
                if response.status_code == 200:
                    content = response.content.decode().lower()
                    self.assertTrue(
                        'email' in content and 'error' in content,
                        f"Invalid email '{email}' was not rejected"
                    )
    
    def test_brute_force_protection(self):
        """Test brute force attack protection."""
        login_url = reverse('account_login')
        
        # Clear cache
        cache.clear()
        
        # Simulate brute force attack
        for i in range(10):
            login_data = {
                'login': self.customer_user.email,
                'password': f'wrongpassword{i}',
            }
            
            response = self.client.post(login_url, login_data)
            
            # After several attempts, should be blocked
            if i > 5:
                self.assertIn(response.status_code, [429, 200])
                if response.status_code == 200:
                    content = response.content.decode().lower()
                    self.assertTrue(
                        'blocked' in content or 
                        'too many' in content or
                        'try again' in content
                    )
    
    def test_account_lockout_mechanism(self):
        """Test account lockout mechanism."""
        # Create test user
        test_user = self.test_data.create_complete_customer(email='<EMAIL>')
        test_user.set_password('TestPass123!')
        test_user.save()
        
        login_url = reverse('account_login')
        
        # Make multiple failed attempts
        for i in range(6):
            login_data = {
                'login': '<EMAIL>',
                'password': 'wrongpassword',
            }
            
            response = self.client.post(login_url, login_data)
        
        # Verify failed attempts were logged
        failed_attempts = LoginHistory.objects.filter(
            user=test_user,
            success=False
        )
        self.assertGreaterEqual(failed_attempts.count(), 5)
        
        # Try with correct password - might still be blocked
        correct_login_data = {
            'login': '<EMAIL>',
            'password': 'TestPass123!',
        }
        
        response = self.client.post(login_url, correct_login_data)
        # Behavior depends on lockout implementation
    
    def test_session_timeout_security(self):
        """Test session timeout security."""
        # Login user
        self.login_user(self.customer_user)
        
        # Access protected page
        response = self.client.get(reverse('accounts_app:dashboard'))
        self.assertEqual(response.status_code, 200)
        
        # Simulate session timeout by manipulating session
        session = self.client.session
        session.set_expiry(-1)  # Expire immediately
        session.save()
        
        # Try to access protected page
        response = self.client.get(reverse('accounts_app:dashboard'))
        # Should redirect to login
        self.assertRedirects(response, '/accounts/login/?next=/dashboard/')
    
    def test_concurrent_session_security(self):
        """Test concurrent session security."""
        # Login from multiple clients
        client1 = Client()
        client2 = Client()
        
        login_data = {
            'login': self.customer_user.email,
            'password': 'TestPass123!',
        }
        
        # Login from first client
        response1 = client1.post(reverse('account_login'), login_data)
        self.assertEqual(response1.status_code, 302)
        
        # Login from second client
        response2 = client2.post(reverse('account_login'), login_data)
        self.assertEqual(response2.status_code, 302)
        
        # Both should be able to access protected resources
        # (unless concurrent session limiting is implemented)
        response1 = client1.get(reverse('accounts_app:dashboard'))
        response2 = client2.get(reverse('accounts_app:dashboard'))
        
        # At least one should work
        self.assertTrue(
            response1.status_code == 200 or response2.status_code == 200
        )
    
    def test_security_logging_and_monitoring(self):
        """Test security event logging and monitoring."""
        # Clear existing events
        SessionSecurityEvent.objects.filter(user=self.customer_user).delete()
        
        # Perform various security-relevant actions
        
        # 1. Failed login
        login_data = {
            'login': self.customer_user.email,
            'password': 'wrongpassword',
        }
        self.client.post(reverse('account_login'), login_data)
        
        # 2. Successful login
        login_data['password'] = 'TestPass123!'
        self.client.post(reverse('account_login'), login_data)
        
        # 3. Logout
        self.client.post(reverse('account_logout'))
        
        # Verify events were logged
        events = SessionSecurityEvent.objects.filter(user=self.customer_user)
        self.assertGreater(events.count(), 0)
        
        # Check for specific event types
        event_types = events.values_list('event_type', flat=True)
        expected_events = ['login_failed', 'login_success', 'logout']
        
        for event_type in expected_events:
            if event_type in ['login_failed', 'login_success', 'logout']:
                # These should be logged if implemented
                pass
    
    def test_input_sanitization(self):
        """Test input sanitization across authentication forms."""
        malicious_inputs = [
            '<script>alert("xss")</script>',
            '"><script>alert("xss")</script>',
            'javascript:alert("xss")',
            '<img src=x onerror=alert("xss")>',
            "'; DROP TABLE users; --",
            "' OR '1'='1",
        ]
        
        for malicious_input in malicious_inputs:
            with self.subTest(input=malicious_input):
                # Test in signup form
                signup_data = self.test_data.generate_form_data('signup', User.CUSTOMER)
                signup_data['first_name'] = malicious_input
                signup_data['last_name'] = malicious_input
                
                response = self.client.post(reverse('accounts_app:customer_signup'), signup_data)
                
                # Should not reflect malicious input unescaped
                if response.status_code == 200:
                    content = response.content.decode()
                    self.assertNotIn(malicious_input, content)
    
    def test_authentication_timing_attacks(self):
        """Test protection against timing attacks."""
        # Test login timing for existing vs non-existing users
        existing_email = self.customer_user.email
        nonexisting_email = '<EMAIL>'
        
        # Measure time for existing user (wrong password)
        start_time = time.time()
        self.client.post(reverse('account_login'), {
            'login': existing_email,
            'password': 'wrongpassword',
        })
        existing_user_time = time.time() - start_time
        
        # Measure time for non-existing user
        start_time = time.time()
        self.client.post(reverse('account_login'), {
            'login': nonexisting_email,
            'password': 'wrongpassword',
        })
        nonexisting_user_time = time.time() - start_time
        
        # Times should be similar (within reasonable variance)
        time_difference = abs(existing_user_time - nonexisting_user_time)
        self.assertLess(time_difference, 0.5, "Timing attack vulnerability detected")
