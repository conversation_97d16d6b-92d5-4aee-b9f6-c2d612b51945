# --- Standard Library Imports ---
from unittest.mock import Mock, patch

# --- Django Imports ---
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

# --- Third-party Imports ---
import pytest
from allauth.socialaccount.models import SocialLogin, SocialAccount

# --- Local App Imports ---
from accounts_app.allauth_forms import (
    CozyWishSignupForm,
    CozyWishSocialSignupForm,
    CozyWishLoginForm,
)
from accounts_app.models import CustomerProfile, ServiceProviderProfile

# --- Setup ---
User = get_user_model()


class CozyWishSignupFormTests(ComprehensiveAuthTestCase):
    """Comprehensive test cases for CozyWishSignupForm."""

    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
    
    def test_customer_signup_form_valid(self):
        """Test valid customer signup form."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.CUSTOMER,
            'agree_to_terms': True,
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_service_provider_signup_form_valid(self):
        """Test valid service provider signup form."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            'business_name': 'Test Business',
            'contact_name': 'John Doe',
            'phone': '+**********',
            'address': '123 Main St',
            'city': 'Test City',
            'state': 'CA',
            'zip_code': '12345',
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_service_provider_missing_required_fields(self):
        """Test service provider signup with missing required fields."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            # Missing business fields
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        
        # Check that required fields have errors
        required_fields = ['business_name', 'contact_name', 'phone', 'address', 'city', 'state', 'zip_code']
        for field in required_fields:
            self.assertIn(field, form.errors)
    
    def test_duplicate_email_validation(self):
        """Test email uniqueness validation."""
        # Create existing user
        User.objects.create_user(email='<EMAIL>', password='testpass123!')
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.CUSTOMER,
            'agree_to_terms': True,
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_terms_agreement_required(self):
        """Test that terms agreement is required."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.CUSTOMER,
            'agree_to_terms': False,
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('agree_to_terms', form.errors)
    
    def test_phone_normalization(self):
        """Test phone number normalization."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            'business_name': 'Test Business',
            'contact_name': 'John Doe',
            'phone': '(*************',  # Format that needs normalization
            'address': '123 Main St',
            'city': 'Test City',
            'state': 'CA',
            'zip_code': '12345',
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['phone'], '+**********')
    
    def test_form_save_sets_role_attribute(self):
        """Test that form save method sets role attribute for adapter."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            'business_name': 'Test Business',
            'contact_name': 'John Doe',
            'phone': '+**********',
            'address': '123 Main St',
            'city': 'Test City',
            'state': 'CA',
            'zip_code': '12345',
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertTrue(form.is_valid())

        request = self.factory.post('/accounts/signup/')
        user = form.save(request)

        # Check that role attribute is set on form
        self.assertEqual(form.role, User.SERVICE_PROVIDER)
        # Check that user was created with correct role
        self.assertEqual(user.role, User.SERVICE_PROVIDER)
        self.assertEqual(user.email, '<EMAIL>')

    def test_form_field_validation_edge_cases(self):
        """Test form field validation edge cases."""
        # Test email validation edge cases
        invalid_emails = [
            'invalid-email',
            '@example.com',
            'test@',
            '<EMAIL>',
            'test@example',
            'test@.com',
            'test@com.',
        ]

        for email in invalid_emails:
            with self.subTest(email=email):
                form_data = self.test_data.generate_form_data('signup', User.CUSTOMER)
                form_data['email'] = email
                form = CozyWishSignupForm(data=form_data)
                self.assertFalse(form.is_valid())
                self.assertIn('email', form.errors)

    def test_password_validation_comprehensive(self):
        """Test comprehensive password validation."""
        weak_passwords = [
            '123',
            'password',
            '12345678',
            'qwerty',
            'admin',
            '<EMAIL>',  # Same as email
        ]

        for password in weak_passwords:
            with self.subTest(password=password):
                form_data = self.test_data.generate_form_data('signup', User.CUSTOMER)
                form_data['password1'] = password
                form_data['password2'] = password
                form = CozyWishSignupForm(data=form_data)
                self.assertFalse(form.is_valid())
                # Should have password errors
                self.assertTrue(
                    'password1' in form.errors or 'password2' in form.errors or
                    '__all__' in form.errors
                )

    def test_password_mismatch_validation(self):
        """Test password mismatch validation."""
        form_data = self.test_data.generate_form_data('signup', User.CUSTOMER)
        form_data['password1'] = 'TestPass123!'
        form_data['password2'] = 'DifferentPass123!'

        form = CozyWishSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('password2', form.errors)

    def test_business_fields_conditional_validation(self):
        """Test conditional validation of business fields."""
        # Service provider should require business fields
        form_data = {
            'email': '<EMAIL>',
            'password1': 'TestPass123!',
            'password2': 'TestPass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            # Missing business fields
        }

        form = CozyWishSignupForm(data=form_data)
        self.assertFalse(form.is_valid())

        # Should have errors for required business fields
        business_fields = ['business_name', 'contact_name', 'phone', 'address', 'city', 'state', 'zip_code']
        for field in business_fields:
            self.assertIn(field, form.errors, f"Missing error for required field: {field}")

    def test_customer_fields_validation(self):
        """Test customer-specific field validation."""
        form_data = self.test_data.generate_form_data('signup', User.CUSTOMER)

        # Test with invalid phone number
        form_data['phone'] = 'invalid-phone'
        form = CozyWishSignupForm(data=form_data)
        if 'phone' in form.fields:  # Only test if phone field exists for customers
            self.assertFalse(form.is_valid())
            self.assertIn('phone', form.errors)

    def test_form_security_validation(self):
        """Test form security validation."""
        # Test XSS prevention
        xss_payloads = self.test_data.generate_security_test_data()['xss_payloads']

        for payload in xss_payloads[:2]:  # Test first 2 payloads
            with self.subTest(payload=payload):
                form_data = self.test_data.generate_form_data('signup', User.CUSTOMER)
                form_data['first_name'] = payload
                form_data['last_name'] = payload

                form = CozyWishSignupForm(data=form_data)
                if form.is_valid():
                    # If form is valid, ensure data is sanitized
                    self.assertNotEqual(form.cleaned_data['first_name'], payload)
                    self.assertNotEqual(form.cleaned_data['last_name'], payload)

    def test_form_accessibility_features(self):
        """Test form accessibility features."""
        form = CozyWishSignupForm()

        # Check that form has proper labels
        for field_name, field in form.fields.items():
            self.assertIsNotNone(field.label, f"Field {field_name} should have a label")

        # Check for help text on important fields
        important_fields = ['email', 'password1', 'password2']
        for field_name in important_fields:
            if field_name in form.fields:
                field = form.fields[field_name]
                # Should have help text or proper widget attributes
                self.assertTrue(
                    field.help_text or
                    'aria-describedby' in field.widget.attrs or
                    'placeholder' in field.widget.attrs,
                    f"Field {field_name} should have accessibility features"
                )


class CozyWishSocialSignupFormTests(ComprehensiveAuthTestCase, MockSocialLoginMixin):
    """Comprehensive test cases for CozyWishSocialSignupForm."""

    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
        # Create a mock sociallogin object
        self.mock_sociallogin = self.create_mock_sociallogin()

    def test_social_signup_form_valid(self):
        """Test valid social signup form."""
        form_data = {
            'role': User.CUSTOMER,
            'agree_to_terms': True,
        }
        form = CozyWishSocialSignupForm(data=form_data, sociallogin=self.mock_sociallogin)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")

    def test_social_signup_terms_required(self):
        """Test that terms agreement is required for social signup."""
        form_data = {
            'role': User.CUSTOMER,
            'agree_to_terms': False,
        }
        form = CozyWishSocialSignupForm(data=form_data, sociallogin=self.mock_sociallogin)
        self.assertFalse(form.is_valid())
        self.assertIn('agree_to_terms', form.errors)

    def test_social_signup_save_sets_role(self):
        """Test that social signup form save sets role attribute."""
        # Create a mock user for the sociallogin
        mock_user = User(email='<EMAIL>', role=User.CUSTOMER)
        self.mock_sociallogin.user = mock_user

        form_data = {
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
        }
        form = CozyWishSocialSignupForm(data=form_data, sociallogin=self.mock_sociallogin)
        self.assertTrue(form.is_valid())

        request = self.factory.post('/accounts/social/signup/')
        user = form.save(request)

        # Check that role attribute is set on form
        self.assertEqual(form.role, User.SERVICE_PROVIDER)
        # Check that user role was updated
        self.assertEqual(user.role, User.SERVICE_PROVIDER)

    def test_social_signup_form_provider_specific_data(self):
        """Test social signup form with provider-specific data."""
        providers = ['google', 'facebook', 'twitter', 'linkedin']

        for provider in providers:
            with self.subTest(provider=provider):
                sociallogin = self.create_mock_sociallogin(provider)

                form_data = {
                    'role': User.CUSTOMER,
                    'agree_to_terms': True,
                }

                form = CozyWishSocialSignupForm(data=form_data, sociallogin=sociallogin)
                self.assertTrue(form.is_valid(), f"Form should be valid for {provider}")

    def test_social_signup_form_business_detection(self):
        """Test automatic business detection in social signup."""
        # Create business-like social login
        business_sociallogin = self.create_mock_sociallogin('google', {
            'email': '<EMAIL>',
            'name': 'Business Corp',
            'given_name': 'Business',
            'family_name': 'Corp'
        })

        form_data = {
            'role': User.CUSTOMER,  # User selects customer but data suggests business
            'agree_to_terms': True,
        }

        form = CozyWishSocialSignupForm(data=form_data, sociallogin=business_sociallogin)
        self.assertTrue(form.is_valid())

        # Form should handle business detection logic
        request = self.factory.post('/accounts/social/signup/')
        user = form.save(request)

        # Role might be adjusted based on business detection
        self.assertIn(user.role, [User.CUSTOMER, User.SERVICE_PROVIDER])

    def test_social_signup_form_error_handling(self):
        """Test error handling in social signup form."""
        # Test with invalid sociallogin
        form_data = {
            'role': User.CUSTOMER,
            'agree_to_terms': True,
        }

        # Test with None sociallogin
        form = CozyWishSocialSignupForm(data=form_data, sociallogin=None)
        # Should handle gracefully
        self.assertIsNotNone(form)

        # Test with malformed sociallogin
        malformed_sociallogin = Mock()
        malformed_sociallogin.account = None

        form = CozyWishSocialSignupForm(data=form_data, sociallogin=malformed_sociallogin)
        # Should handle gracefully
        self.assertIsNotNone(form)

    def test_social_signup_form_security_validation(self):
        """Test security validation in social signup form."""
        # Test with malicious social data
        malicious_sociallogin = self.create_mock_sociallogin('google', {
            'email': '<script>alert("xss")</script>@example.com',
            'name': '<script>alert("xss")</script>',
            'given_name': '"><script>alert("xss")</script>',
        })

        form_data = {
            'role': User.CUSTOMER,
            'agree_to_terms': True,
        }

        form = CozyWishSocialSignupForm(data=form_data, sociallogin=malicious_sociallogin)

        if form.is_valid():
            request = self.factory.post('/accounts/social/signup/')
            user = form.save(request)

            # Should sanitize malicious input
            self.assertNotIn('<script>', user.first_name or '')
            self.assertNotIn('<script>', user.last_name or '')
            self.assertNotIn('<script>', user.email)


class CozyWishLoginFormTests(ComprehensiveAuthTestCase):
    """Comprehensive test cases for CozyWishLoginForm."""

    def setUp(self):
        """Set up test dependencies."""
        super().setUp()
        # Create additional test users
        self.active_customer = self.test_data.create_complete_customer(
            email='<EMAIL>'
        )
        self.active_customer.set_password('testpass123!')
        self.active_customer.save()

        self.inactive_provider = self.test_data.create_complete_service_provider(
            email='<EMAIL>'
        )
        self.inactive_provider.set_password('testpass123!')
        self.inactive_provider.is_active = False
        self.inactive_provider.save()
    
    def test_login_form_styling(self):
        """Test that login form has proper styling."""
        form = CozyWishLoginForm()
        
        # Check that login field has proper attributes
        login_widget = form.fields['login'].widget
        self.assertIn('form-control', login_widget.attrs.get('class', ''))
        self.assertEqual(form.fields['login'].label, 'Email Address')
        
        # Check that password field has proper attributes
        password_widget = form.fields['password'].widget
        self.assertIn('form-control', password_widget.attrs.get('class', ''))
    
    @patch('allauth.account.forms.LoginForm.clean')
    def test_login_form_inactive_provider_message(self, mock_parent_clean):
        """Test custom message for inactive service provider."""
        # Mock parent clean to set the user
        mock_parent_clean.return_value = {}
        
        form_data = {
            'login': '<EMAIL>',
            'password': 'testpass123!',
        }
        form = CozyWishLoginForm(data=form_data)
        form.user = self.inactive_provider
        
        with self.assertRaises(ValidationError) as cm:
            form.clean()
        
        self.assertIn('verify your email', str(cm.exception))

    def test_login_form_rate_limiting_integration(self):
        """Test login form integration with rate limiting."""
        form_data = {
            'login': '<EMAIL>',
            'password': 'wrongpassword',
        }

        # Test multiple failed login attempts
        for i in range(3):
            form = CozyWishLoginForm(data=form_data)
            # Should still create form but may have rate limiting logic
            self.assertIsNotNone(form)

    def test_login_form_security_features(self):
        """Test login form security features."""
        # Test with malicious input
        malicious_data = {
            'login': '<script>alert("xss")</script>@example.com',
            'password': '"><script>alert("xss")</script>',
        }

        form = CozyWishLoginForm(data=malicious_data)
        # Should handle malicious input gracefully
        self.assertIsNotNone(form)

        if form.is_valid():
            # Should sanitize input
            self.assertNotIn('<script>', form.cleaned_data['login'])

    def test_login_form_accessibility_compliance(self):
        """Test login form accessibility compliance."""
        form = CozyWishLoginForm()

        # Check for proper field labels
        self.assertEqual(form.fields['login'].label, 'Email Address')

        # Check for proper widget attributes
        login_widget = form.fields['login'].widget
        self.assertIn('form-control', login_widget.attrs.get('class', ''))
        self.assertEqual(login_widget.attrs.get('autocomplete'), 'email')

        password_widget = form.fields['password'].widget
        self.assertIn('form-control', password_widget.attrs.get('class', ''))

    def test_login_form_remember_me_functionality(self):
        """Test remember me functionality if implemented."""
        form = CozyWishLoginForm()

        # Check if remember me field exists
        if 'remember' in form.fields:
            form_data = {
                'login': '<EMAIL>',
                'password': 'testpass123!',
                'remember': True,
            }

            form = CozyWishLoginForm(data=form_data)
            if form.is_valid():
                self.assertTrue(form.cleaned_data.get('remember', False))

    def test_login_form_error_messages(self):
        """Test login form error messages."""
        # Test with invalid credentials
        form_data = {
            'login': '<EMAIL>',
            'password': 'wrongpassword',
        }

        form = CozyWishLoginForm(data=form_data)

        # Form validation behavior depends on allauth implementation
        # Just ensure form handles invalid data gracefully
        self.assertIsNotNone(form)

    def test_login_form_performance(self):
        """Test login form performance."""
        form_data = {
            'login': '<EMAIL>',
            'password': 'testpass123!',
        }

        # Test form creation and validation performance
        start_time = time.time()
        form = CozyWishLoginForm(data=form_data)
        form.is_valid()
        end_time = time.time()

        # Should complete quickly (under 1 second)
        self.assertLess(end_time - start_time, 1.0)

    def test_login_form_csrf_integration(self):
        """Test login form CSRF integration."""
        # Test that form works with CSRF middleware
        request = self.create_authenticated_request(
            user=None,
            path='/accounts/login/',
            method='POST'
        )

        form_data = {
            'login': '<EMAIL>',
            'password': 'testpass123!',
        }

        form = CozyWishLoginForm(data=form_data)
        # Should handle CSRF requirements
        self.assertIsNotNone(form)

    def test_login_form_internationalization(self):
        """Test login form internationalization support."""
        form = CozyWishLoginForm()

        # Check that labels and help text support translation
        for field_name, field in form.fields.items():
            if field.label:
                self.assertIsInstance(field.label, (str, type(field.label)))
            if field.help_text:
                self.assertIsInstance(field.help_text, (str, type(field.help_text)))
