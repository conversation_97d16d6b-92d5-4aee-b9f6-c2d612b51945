# --- Django Imports ---
from django import forms
from django.contrib.auth.forms import PasswordChangeForm, SetPasswordForm
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import authenticate

# --- Local App Imports ---
from ..utils.password_security import (
    validate_password,
    check_password_history,
    save_password_to_history,
    get_password_policy_text,
)
from ..models.security import PasswordSecurityEvent
from ..mixins.validation_mixins import SecurityValidationMixin


class PasswordValidationMixin:
    """
    Mixin for forms that need comprehensive password validation.
    """
    
    def clean_password1(self):
        """Validate new password with comprehensive security checks."""
        password = self.cleaned_data.get('password1')
        
        if not password:
            return password
        
        # Get user for personalized validation
        user = getattr(self, 'user', None)
        
        # Comprehensive password validation
        validation_result = validate_password(password, user)
        
        if not validation_result['is_valid']:
            # Log password policy violation
            PasswordSecurityEvent.log_event(
                PasswordSecurityEvent.PASSWORD_POLICY_VIOLATION,
                user=user,
                details={
                    'errors': validation_result['errors'],
                    'warnings': validation_result['warnings'],
                    'score': validation_result['score'],
                    'strength_level': validation_result['strength_level'],
                }
            )
            
            # Raise validation error with all issues
            error_messages = validation_result['errors']
            raise ValidationError(error_messages)
        
        # Check password history
        if user and check_password_history(user, password):
            PasswordSecurityEvent.log_event(
                PasswordSecurityEvent.PASSWORD_REUSE_ATTEMPTED,
                user=user,
                details={'password_strength': validation_result['strength_level']}
            )
            raise ValidationError(
                _('You cannot reuse one of your recent passwords. Please choose a different password.')
            )
        
        # Log weak password detection
        if validation_result['strength_level'] in ['weak', 'very_weak']:
            PasswordSecurityEvent.log_event(
                PasswordSecurityEvent.WEAK_PASSWORD_DETECTED,
                user=user,
                details={
                    'strength_level': validation_result['strength_level'],
                    'score': validation_result['score'],
                    'feedback': validation_result['feedback'],
                },
                is_suspicious=True
            )
        
        return password
    
    def clean(self):
        """Enhanced clean method for password confirmation."""
        cleaned_data = super().clean()
        password1 = cleaned_data.get('password1')
        password2 = cleaned_data.get('password2')
        
        if password1 and password2:
            if password1 != password2:
                raise ValidationError({
                    'password2': _('The two password fields didn\'t match.')
                })
        
        return cleaned_data
    
    def save(self, commit=True):
        """Save password and update history."""
        user = super().save(commit=commit)
        
        if commit:
            password = self.cleaned_data.get('password1')
            if password:
                # Save to password history
                save_password_to_history(user, password)
                
                # Log password change
                PasswordSecurityEvent.log_event(
                    PasswordSecurityEvent.PASSWORD_CHANGED,
                    user=user,
                    request=getattr(self, 'request', None),
                    details={'method': 'form_submission'}
                )
        
        return user


class EnhancedPasswordChangeForm(PasswordValidationMixin, PasswordChangeForm):
    """
    Enhanced password change form with comprehensive security validation.
    """
    
    def __init__(self, user, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(user, *args, **kwargs)
        
        # Add password policy help text
        policy_text = get_password_policy_text()
        self.fields['new_password1'].help_text = policy_text
        
        # Add security attributes
        self.fields['old_password'].widget.attrs.update({
            'class': 'form-control',
            'autocomplete': 'current-password',
            'placeholder': 'Current password',
        })
        
        self.fields['new_password1'].widget.attrs.update({
            'class': 'form-control password-strength-input',
            'autocomplete': 'new-password',
            'placeholder': 'New password',
            'data-password-strength': 'true',
        })
        
        self.fields['new_password2'].widget.attrs.update({
            'class': 'form-control',
            'autocomplete': 'new-password',
            'placeholder': 'Confirm new password',
        })
    
    def clean_old_password(self):
        """Validate current password."""
        old_password = self.cleaned_data.get('old_password')
        
        if not old_password:
            return old_password
        
        if not self.user.check_password(old_password):
            # Log failed password verification
            PasswordSecurityEvent.log_event(
                'password_verification_failed',
                user=self.user,
                request=self.request,
                details={'context': 'password_change'},
                is_suspicious=True
            )
            raise ValidationError(_('Your old password was entered incorrectly. Please enter it again.'))
        
        return old_password


class EnhancedSetPasswordForm(PasswordValidationMixin, SetPasswordForm):
    """
    Enhanced set password form for password resets with security validation.
    """
    
    def __init__(self, user, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(user, *args, **kwargs)
        
        # Add password policy help text
        policy_text = get_password_policy_text()
        self.fields['new_password1'].help_text = policy_text
        
        # Add security attributes
        self.fields['new_password1'].widget.attrs.update({
            'class': 'form-control password-strength-input',
            'autocomplete': 'new-password',
            'placeholder': 'New password',
            'data-password-strength': 'true',
        })
        
        self.fields['new_password2'].widget.attrs.update({
            'class': 'form-control',
            'autocomplete': 'new-password',
            'placeholder': 'Confirm new password',
        })


class PasswordStrengthForm(SecurityValidationMixin, forms.Form):
    """
    Form for checking password strength without saving.
    """
    
    password = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control password-strength-input',
            'placeholder': 'Enter password to check strength',
            'data-password-strength': 'true',
        }),
        help_text=get_password_policy_text()
    )
    
    def __init__(self, user=None, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_password(self):
        """Validate password strength."""
        password = self.cleaned_data.get('password')
        
        if not password:
            return password
        
        # Comprehensive password validation
        validation_result = validate_password(password, self.user)
        
        # Store validation result for template use
        self.validation_result = validation_result
        
        if not validation_result['is_valid']:
            raise ValidationError(validation_result['errors'])
        
        return password
    
    def get_strength_data(self):
        """Get password strength data for JavaScript."""
        if hasattr(self, 'validation_result'):
            return {
                'score': self.validation_result['score'],
                'strength_level': self.validation_result['strength_level'],
                'feedback': self.validation_result['feedback'],
                'warnings': self.validation_result['warnings'],
                'is_valid': self.validation_result['is_valid'],
            }
        return None


class PasswordPolicyForm(forms.Form):
    """
    Form for displaying and managing password policy settings.
    """
    
    min_length = forms.IntegerField(
        label=_('Minimum Length'),
        min_value=6,
        max_value=50,
        initial=8,
        help_text=_('Minimum number of characters required')
    )
    
    max_length = forms.IntegerField(
        label=_('Maximum Length'),
        min_value=20,
        max_value=200,
        initial=128,
        help_text=_('Maximum number of characters allowed')
    )
    
    require_uppercase = forms.BooleanField(
        label=_('Require Uppercase Letters'),
        required=False,
        initial=True,
        help_text=_('Password must contain at least one uppercase letter')
    )
    
    require_lowercase = forms.BooleanField(
        label=_('Require Lowercase Letters'),
        required=False,
        initial=True,
        help_text=_('Password must contain at least one lowercase letter')
    )
    
    require_numbers = forms.BooleanField(
        label=_('Require Numbers'),
        required=False,
        initial=True,
        help_text=_('Password must contain at least one number')
    )
    
    require_symbols = forms.BooleanField(
        label=_('Require Special Characters'),
        required=False,
        initial=True,
        help_text=_('Password must contain at least one special character')
    )
    
    history_count = forms.IntegerField(
        label=_('Password History Count'),
        min_value=0,
        max_value=20,
        initial=5,
        help_text=_('Number of previous passwords to remember')
    )
    
    enable_breach_check = forms.BooleanField(
        label=_('Enable Breach Checking'),
        required=False,
        initial=True,
        help_text=_('Check passwords against known data breaches')
    )
    
    def clean(self):
        """Validate policy settings."""
        cleaned_data = super().clean()
        min_length = cleaned_data.get('min_length')
        max_length = cleaned_data.get('max_length')
        
        if min_length and max_length and min_length >= max_length:
            raise ValidationError(_('Maximum length must be greater than minimum length'))
        
        return cleaned_data
