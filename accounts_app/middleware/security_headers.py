# --- Django Imports ---
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.http import HttpResponse
import logging

logger = logging.getLogger(__name__)


class ComprehensiveSecurityHeadersMiddleware(MiddlewareMixin):
    """
    Comprehensive security headers middleware.
    
    Features:
    - Content Security Policy (CSP)
    - HTTP Strict Transport Security (HSTS)
    - X-Frame-Options
    - X-Content-Type-Options
    - X-XSS-Protection
    - Referrer Policy
    - Permissions Policy
    - Cross-Origin policies
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Security headers configuration
        self.enable_hsts = getattr(settings, 'SECURITY_ENABLE_HSTS', True)
        self.hsts_max_age = getattr(settings, 'SECURITY_HSTS_MAX_AGE', 31536000)  # 1 year
        self.hsts_include_subdomains = getattr(settings, 'SECURITY_HSTS_INCLUDE_SUBDOMAINS', True)
        self.hsts_preload = getattr(settings, 'SECURITY_HSTS_PRELOAD', True)
        
        self.enable_csp = getattr(settings, 'SECURITY_ENABLE_CSP', True)
        self.csp_report_only = getattr(settings, 'SECURITY_CSP_REPORT_ONLY', False)
        self.csp_report_uri = getattr(settings, 'SECURITY_CSP_REPORT_URI', '/security/csp-report/')
        
        self.frame_options = getattr(settings, 'SECURITY_FRAME_OPTIONS', 'DENY')
        self.content_type_options = getattr(settings, 'SECURITY_CONTENT_TYPE_OPTIONS', 'nosniff')
        self.xss_protection = getattr(settings, 'SECURITY_XSS_PROTECTION', '1; mode=block')
        self.referrer_policy = getattr(settings, 'SECURITY_REFERRER_POLICY', 'strict-origin-when-cross-origin')
        
        # Paths that need special CSP handling
        self.admin_paths = ['/admin/', '/dashboard/admin/']
        self.api_paths = ['/api/']
        self.auth_paths = ['/accounts/']
        
        super().__init__(get_response)
    
    def process_response(self, request, response):
        """Add comprehensive security headers to response."""
        # Skip for non-HTML responses unless specifically needed
        content_type = response.get('Content-Type', '')
        
        # Add security headers
        self._add_hsts_header(request, response)
        self._add_frame_options(request, response)
        self._add_content_type_options(response)
        self._add_xss_protection(response)
        self._add_referrer_policy(response)
        self._add_permissions_policy(response)
        self._add_cross_origin_policies(response)
        
        # Add CSP header
        if self.enable_csp:
            self._add_csp_header(request, response)
        
        # Add additional security headers for sensitive paths
        if self._is_sensitive_path(request.path):
            self._add_sensitive_path_headers(response)
        
        return response
    
    def _add_hsts_header(self, request, response):
        """Add HTTP Strict Transport Security header."""
        if not self.enable_hsts:
            return
        
        # Only add HSTS for HTTPS requests
        if not request.is_secure() and not settings.DEBUG:
            return
        
        hsts_value = f'max-age={self.hsts_max_age}'
        
        if self.hsts_include_subdomains:
            hsts_value += '; includeSubDomains'
        
        if self.hsts_preload:
            hsts_value += '; preload'
        
        response['Strict-Transport-Security'] = hsts_value
    
    def _add_frame_options(self, request, response):
        """Add X-Frame-Options header."""
        # Allow framing for certain paths if needed
        if self._allows_framing(request.path):
            response['X-Frame-Options'] = 'SAMEORIGIN'
        else:
            response['X-Frame-Options'] = self.frame_options
    
    def _add_content_type_options(self, response):
        """Add X-Content-Type-Options header."""
        response['X-Content-Type-Options'] = self.content_type_options
    
    def _add_xss_protection(self, response):
        """Add X-XSS-Protection header."""
        response['X-XSS-Protection'] = self.xss_protection
    
    def _add_referrer_policy(self, response):
        """Add Referrer-Policy header."""
        response['Referrer-Policy'] = self.referrer_policy
    
    def _add_permissions_policy(self, response):
        """Add Permissions-Policy header."""
        # Restrict potentially dangerous features
        permissions_policy = [
            'camera=()',
            'microphone=()',
            'geolocation=()',
            'payment=()',
            'usb=()',
            'magnetometer=()',
            'gyroscope=()',
            'accelerometer=()',
            'ambient-light-sensor=()',
            'autoplay=()',
            'encrypted-media=()',
            'fullscreen=(self)',
            'picture-in-picture=()',
        ]
        
        response['Permissions-Policy'] = ', '.join(permissions_policy)
    
    def _add_cross_origin_policies(self, response):
        """Add Cross-Origin policies."""
        # Cross-Origin-Embedder-Policy
        response['Cross-Origin-Embedder-Policy'] = 'require-corp'
        
        # Cross-Origin-Opener-Policy
        response['Cross-Origin-Opener-Policy'] = 'same-origin'
        
        # Cross-Origin-Resource-Policy
        response['Cross-Origin-Resource-Policy'] = 'same-origin'
    
    def _add_csp_header(self, request, response):
        """Add Content Security Policy header."""
        csp_directives = self._build_csp_directives(request)
        csp_header = '; '.join([f"{directive} {sources}" for directive, sources in csp_directives.items()])
        
        # Add report URI if configured
        if self.csp_report_uri:
            csp_header += f'; report-uri {self.csp_report_uri}'
        
        # Use report-only mode if configured
        header_name = 'Content-Security-Policy-Report-Only' if self.csp_report_only else 'Content-Security-Policy'
        response[header_name] = csp_header
    
    def _build_csp_directives(self, request):
        """Build CSP directives based on request path."""
        # Base CSP directives
        directives = {
            'default-src': "'self'",
            'script-src': "'self'",
            'style-src': "'self' 'unsafe-inline'",
            'img-src': "'self' data: https:",
            'font-src': "'self' https:",
            'connect-src': "'self'",
            'media-src': "'self'",
            'object-src': "'none'",
            'base-uri': "'self'",
            'form-action': "'self'",
            'frame-ancestors': "'none'",
            'upgrade-insecure-requests': "",
        }
        
        # Adjust CSP based on path
        if self._is_admin_path(request.path):
            # Admin pages may need more permissive CSP
            directives['script-src'] = "'self' 'unsafe-inline' 'unsafe-eval'"
            directives['style-src'] = "'self' 'unsafe-inline'"
            directives['img-src'] = "'self' data: https: blob:"
        
        elif self._is_api_path(request.path):
            # API endpoints have different requirements
            directives['connect-src'] = "'self' https:"
            directives.pop('upgrade-insecure-requests', None)
        
        elif self._is_auth_path(request.path):
            # Authentication pages need specific allowances
            directives['script-src'] = "'self' 'unsafe-inline'"
            directives['style-src'] = "'self' 'unsafe-inline'"
            directives['connect-src'] = "'self' https:"
        
        # Add external service domains if needed
        external_domains = getattr(settings, 'CSP_EXTERNAL_DOMAINS', [])
        if external_domains:
            for domain in external_domains:
                directives['connect-src'] += f" {domain}"
                directives['img-src'] += f" {domain}"
        
        return directives
    
    def _add_sensitive_path_headers(self, response):
        """Add additional headers for sensitive paths."""
        # Disable caching for sensitive pages
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate, private'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        
        # Additional security for sensitive operations
        response['X-Permitted-Cross-Domain-Policies'] = 'none'
        response['X-Download-Options'] = 'noopen'
    
    def _is_sensitive_path(self, path):
        """Check if path is sensitive and needs extra security."""
        sensitive_paths = [
            '/accounts/login/',
            '/accounts/signup/',
            '/accounts/password/',
            '/accounts/email/',
            '/dashboard/admin/',
            '/admin/',
            '/accounts/sessions/',
            '/accounts/security/',
        ]
        
        return any(path.startswith(sensitive_path) for sensitive_path in sensitive_paths)
    
    def _is_admin_path(self, path):
        """Check if path is an admin path."""
        return any(path.startswith(admin_path) for admin_path in self.admin_paths)
    
    def _is_api_path(self, path):
        """Check if path is an API path."""
        return any(path.startswith(api_path) for api_path in self.api_paths)
    
    def _is_auth_path(self, path):
        """Check if path is an authentication path."""
        return any(path.startswith(auth_path) for auth_path in self.auth_paths)
    
    def _allows_framing(self, path):
        """Check if path allows framing (for embeds, etc.)."""
        # Paths that might need to be framed
        frameable_paths = [
            '/embed/',
            '/widget/',
        ]
        
        return any(path.startswith(frameable_path) for frameable_path in frameable_paths)
