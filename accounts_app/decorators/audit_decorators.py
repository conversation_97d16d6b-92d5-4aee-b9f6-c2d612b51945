# --- Standard Library Imports ---
from functools import wraps
import time
import logging

# --- Django Imports ---
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required

# --- Local App Imports ---
from ..utils.audit_logging import (
    log_user_activity,
    log_admin_action,
    log_data_access,
    log_performance_event,
    log_security_event,
)

logger = logging.getLogger(__name__)


def audit_user_activity(activity_type: str, target_object_attr: str = None, 
                       details_func=None):
    """
    Decorator to automatically log user activities.
    
    Args:
        activity_type: Type of activity being performed
        target_object_attr: Attribute name to get target object from view
        details_func: Function to extract additional details
    
    Usage:
        @audit_user_activity('profile_update')
        def update_profile(request):
            pass
        
        @audit_user_activity('venue_create', target_object_attr='venue')
        def create_venue(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            # Execute the view
            response = view_func(request, *args, **kwargs)
            
            # Extract target object if specified
            target_object = None
            if target_object_attr and hasattr(request, target_object_attr):
                target_object = getattr(request, target_object_attr)
            
            # Extract additional details
            details = {}
            if details_func:
                try:
                    details = details_func(request, response, *args, **kwargs)
                except Exception as e:
                    logger.warning(f"Failed to extract audit details: {str(e)}")
            
            # Log the activity
            log_user_activity(
                activity_type=activity_type,
                user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                request=request,
                target_object=target_object,
                details=details
            )
            
            return response
        
        return wrapped_view
    return decorator


def audit_admin_action(action_type: str, target_user_param: str = None):
    """
    Decorator to automatically log admin actions.
    
    Args:
        action_type: Type of admin action
        target_user_param: Parameter name containing target user
    
    Usage:
        @audit_admin_action('user_suspend', target_user_param='user_id')
        def suspend_user(request, user_id):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            # Extract target user if specified
            target_user = None
            if target_user_param and target_user_param in kwargs:
                from django.contrib.auth import get_user_model
                User = get_user_model()
                try:
                    target_user = User.objects.get(id=kwargs[target_user_param])
                except User.DoesNotExist:
                    pass
            
            # Execute the view
            response = view_func(request, *args, **kwargs)
            
            # Log the admin action
            log_admin_action(
                admin_user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                action=action_type,
                target_user=target_user,
                request=request,
                details={
                    'view_args': args,
                    'view_kwargs': {k: v for k, v in kwargs.items() if k != target_user_param},
                }
            )
            
            return response
        
        return wrapped_view
    return decorator


def audit_data_access(resource_type: str, action: str, resource_id_param: str = 'pk'):
    """
    Decorator to automatically log data access.
    
    Args:
        resource_type: Type of resource being accessed
        action: Action being performed (read, create, update, delete)
        resource_id_param: Parameter name containing resource ID
    
    Usage:
        @audit_data_access('venue', 'read', 'venue_id')
        def view_venue(request, venue_id):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            # Extract resource ID
            resource_id = kwargs.get(resource_id_param, 'unknown')
            
            # Execute the view
            response = view_func(request, *args, **kwargs)
            
            # Log the data access
            log_data_access(
                user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                resource_type=resource_type,
                resource_id=str(resource_id),
                action=action,
                request=request,
                details={
                    'view_name': view_func.__name__,
                    'response_status': getattr(response, 'status_code', None),
                }
            )
            
            return response
        
        return wrapped_view
    return decorator


def audit_performance(component: str = None, threshold: float = 1.0):
    """
    Decorator to automatically log performance metrics.
    
    Args:
        component: Component being measured
        threshold: Log only if duration exceeds threshold (seconds)
    
    Usage:
        @audit_performance('database_query', threshold=0.5)
        def slow_query_view(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            start_time = time.time()
            
            # Execute the view
            response = view_func(request, *args, **kwargs)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log if duration exceeds threshold
            if duration >= threshold:
                log_performance_event(
                    event_type='view_execution',
                    duration=duration,
                    component=component or view_func.__name__,
                    details={
                        'view_name': view_func.__name__,
                        'method': request.method,
                        'path': request.path,
                        'response_status': getattr(response, 'status_code', None),
                    }
                )
            
            return response
        
        return wrapped_view
    return decorator


def audit_security_sensitive(event_type: str = None, severity: str = 'medium'):
    """
    Decorator to log access to security-sensitive views.
    
    Args:
        event_type: Type of security event
        severity: Event severity level
    
    Usage:
        @audit_security_sensitive('admin_panel_access', 'high')
        def admin_panel(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            # Log security event
            log_security_event(
                event_type=event_type or f'access_{view_func.__name__}',
                user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                request=request,
                severity=severity,
                details={
                    'view_name': view_func.__name__,
                    'view_args': args,
                    'view_kwargs': kwargs,
                }
            )
            
            # Execute the view
            return view_func(request, *args, **kwargs)
        
        return wrapped_view
    return decorator


class AuditMixin:
    """
    Mixin for class-based views to add automatic audit logging.
    
    Usage:
        class MyView(AuditMixin, View):
            audit_activity_type = 'my_activity'
            audit_resource_type = 'my_resource'
            audit_action = 'read'
    """
    
    audit_activity_type = None
    audit_resource_type = None
    audit_action = 'read'
    audit_performance_threshold = 1.0
    audit_security_event_type = None
    audit_security_severity = 'medium'
    
    def dispatch(self, request, *args, **kwargs):
        start_time = time.time()
        
        # Log security event if configured
        if self.audit_security_event_type:
            log_security_event(
                event_type=self.audit_security_event_type,
                user=request.user if request.user.is_authenticated else None,
                request=request,
                severity=self.audit_security_severity,
                details={
                    'view_class': self.__class__.__name__,
                    'view_args': args,
                    'view_kwargs': kwargs,
                }
            )
        
        # Execute the view
        response = super().dispatch(request, *args, **kwargs)
        
        # Log user activity if configured
        if self.audit_activity_type:
            log_user_activity(
                activity_type=self.audit_activity_type,
                user=request.user if request.user.is_authenticated else None,
                request=request,
                details={
                    'view_class': self.__class__.__name__,
                    'response_status': getattr(response, 'status_code', None),
                }
            )
        
        # Log data access if configured
        if self.audit_resource_type:
            resource_id = kwargs.get('pk', kwargs.get('id', 'unknown'))
            log_data_access(
                user=request.user if request.user.is_authenticated else None,
                resource_type=self.audit_resource_type,
                resource_id=str(resource_id),
                action=self.audit_action,
                request=request,
                details={
                    'view_class': self.__class__.__name__,
                    'response_status': getattr(response, 'status_code', None),
                }
            )
        
        # Log performance if threshold exceeded
        duration = time.time() - start_time
        if duration >= self.audit_performance_threshold:
            log_performance_event(
                event_type='view_execution',
                duration=duration,
                component=self.__class__.__name__,
                details={
                    'view_class': self.__class__.__name__,
                    'method': request.method,
                    'path': request.path,
                    'response_status': getattr(response, 'status_code', None),
                }
            )
        
        return response


# Convenience decorators for common patterns
def audit_login_required(activity_type: str = None):
    """Combine login_required with audit logging."""
    def decorator(view_func):
        @login_required
        @audit_user_activity(activity_type or f'access_{view_func.__name__}')
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            return view_func(request, *args, **kwargs)
        return wrapped_view
    return decorator


def audit_admin_required(action_type: str = None):
    """Audit decorator for admin-only views."""
    def decorator(view_func):
        @audit_admin_action(action_type or f'admin_{view_func.__name__}')
        @audit_security_sensitive(f'admin_access_{view_func.__name__}', 'high')
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            if not request.user.is_staff:
                log_security_event(
                    event_type='unauthorized_admin_access_attempt',
                    user=request.user,
                    request=request,
                    severity='high'
                )
                from django.core.exceptions import PermissionDenied
                raise PermissionDenied
            
            return view_func(request, *args, **kwargs)
        return wrapped_view
    return decorator
