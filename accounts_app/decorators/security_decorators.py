# --- Standard Library Imports ---
from functools import wraps
import logging

# --- Django Imports ---
from django.http import HttpResponseForbidden, JsonResponse
from django.shortcuts import redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect

# --- Local App Imports ---
from ..utils.security_utils import get_client_ip, log_security_event
from ..utils.account_security import check_account_lockout, detect_suspicious_activity
from ..utils.security_helpers import contains_xss_patterns, contains_sql_patterns

logger = logging.getLogger(__name__)


def security_headers(view_func):
    """
    Decorator to add security headers to view responses.
    
    Usage:
        @security_headers
        def my_view(request):
            pass
    """
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        response = view_func(request, *args, **kwargs)
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add CSP for sensitive views
        if hasattr(view_func, 'is_sensitive') and view_func.is_sensitive:
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data:; "
                "font-src 'self'; "
                "connect-src 'self'"
            )
        
        return response
    
    return wrapped_view


def require_secure_connection(view_func):
    """
    Decorator to require HTTPS connection.
    
    Usage:
        @require_secure_connection
        def secure_view(request):
            pass
    """
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        if not request.is_secure() and not settings.DEBUG:
            log_security_event(
                'insecure_connection_attempt',
                request=request,
                details={
                    'view': view_func.__name__,
                    'path': request.path,
                }
            )
            
            # Redirect to HTTPS
            secure_url = request.build_absolute_uri().replace('http://', 'https://')
            return redirect(secure_url)
        
        return view_func(request, *args, **kwargs)
    
    return wrapped_view


def check_account_security(view_func):
    """
    Decorator to check account security before allowing access.
    
    Usage:
        @check_account_security
        def protected_view(request):
            pass
    """
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return view_func(request, *args, **kwargs)
        
        client_ip = get_client_ip(request)
        
        # Check account lockout
        lockout_result = check_account_lockout(request.user, client_ip)
        if lockout_result['is_locked']:
            log_security_event(
                'locked_account_access_attempt',
                user=request.user,
                request=request,
                details=lockout_result
            )
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': 'account_locked',
                    'message': 'Your account is temporarily locked.',
                    'unlock_time': lockout_result['unlock_time'].isoformat() if lockout_result['unlock_time'] else None
                }, status=403)
            else:
                messages.error(request, 'Your account is temporarily locked due to security concerns.')
                return redirect('accounts_app:rate_limit_exceeded')
        
        # Detect suspicious activity
        suspicious_activities = detect_suspicious_activity(request.user, request)
        if suspicious_activities:
            # Log but don't block (just monitor)
            for activity in suspicious_activities:
                log_security_event(
                    'suspicious_activity_during_access',
                    user=request.user,
                    request=request,
                    details=activity
                )
        
        return view_func(request, *args, **kwargs)
    
    return wrapped_view


def validate_input_security(check_xss=True, check_sql=True):
    """
    Decorator to validate input for security threats.
    
    Args:
        check_xss: Whether to check for XSS patterns
        check_sql: Whether to check for SQL injection patterns
    
    Usage:
        @validate_input_security(check_xss=True, check_sql=True)
        def form_view(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            if request.method in ['POST', 'PUT', 'PATCH']:
                violations = []
                
                # Check POST data
                for key, value in request.POST.items():
                    if isinstance(value, str):
                        if check_xss and contains_xss_patterns(value):
                            violations.append({
                                'type': 'xss',
                                'field': key,
                                'value': value[:100]  # Truncate for logging
                            })
                        
                        if check_sql and contains_sql_patterns(value):
                            violations.append({
                                'type': 'sql_injection',
                                'field': key,
                                'value': value[:100]  # Truncate for logging
                            })
                
                # Log violations
                if violations:
                    log_security_event(
                        'input_security_violation',
                        user=request.user if request.user.is_authenticated else None,
                        request=request,
                        severity='high',
                        details={
                            'view': view_func.__name__,
                            'violations': violations
                        }
                    )
                    
                    # Block request with violations
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({
                            'error': 'security_violation',
                            'message': 'Request contains suspicious content.'
                        }, status=400)
                    else:
                        messages.error(request, 'Your request contains suspicious content and has been blocked.')
                        return redirect(request.path)
            
            return view_func(request, *args, **kwargs)
        
        return wrapped_view
    return decorator


def require_staff_or_owner(owner_field='user'):
    """
    Decorator to require staff privileges or object ownership.
    
    Args:
        owner_field: Field name that contains the owner
    
    Usage:
        @require_staff_or_owner('user')
        def edit_profile(request, profile_id):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('accounts_app:login')
            
            # Staff users have access to everything
            if request.user.is_staff:
                return view_func(request, *args, **kwargs)
            
            # Check ownership for regular users
            try:
                # Get the object being accessed
                obj_id = kwargs.get('pk') or kwargs.get('id')
                if obj_id:
                    # This is a simplified check - in practice, you'd need to
                    # get the actual object and check ownership
                    # For now, we'll just check if the user ID matches
                    if str(request.user.id) == str(obj_id):
                        return view_func(request, *args, **kwargs)
                
                # Access denied
                log_security_event(
                    'unauthorized_access_attempt',
                    user=request.user,
                    request=request,
                    details={
                        'view': view_func.__name__,
                        'object_id': obj_id,
                        'owner_field': owner_field
                    }
                )
                
                raise PermissionDenied("You don't have permission to access this resource.")
                
            except Exception as e:
                logger.error(f"Error in ownership check: {str(e)}")
                raise PermissionDenied("Access denied.")
        
        return wrapped_view
    return decorator


def log_sensitive_access(activity_type=None, severity='medium'):
    """
    Decorator to log access to sensitive resources.
    
    Args:
        activity_type: Type of activity being logged
        severity: Severity level of the access
    
    Usage:
        @log_sensitive_access('admin_panel_access', 'high')
        def admin_panel(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            # Log the access
            log_security_event(
                activity_type or f'sensitive_access_{view_func.__name__}',
                user=request.user if request.user.is_authenticated else None,
                request=request,
                severity=severity,
                details={
                    'view': view_func.__name__,
                    'args': args,
                    'kwargs': kwargs,
                }
            )
            
            return view_func(request, *args, **kwargs)
        
        return wrapped_view
    return decorator


def require_recent_authentication(max_age=1800):  # 30 minutes
    """
    Decorator to require recent authentication for sensitive operations.
    
    Args:
        max_age: Maximum age of authentication in seconds
    
    Usage:
        @require_recent_authentication(max_age=900)  # 15 minutes
        def change_password(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('accounts_app:login')
            
            # Check last authentication time
            last_auth = request.session.get('last_authentication')
            if last_auth:
                from datetime import datetime, timedelta
                last_auth_time = datetime.fromisoformat(last_auth)
                if datetime.now() - last_auth_time > timedelta(seconds=max_age):
                    # Authentication too old
                    request.session['next_after_reauth'] = request.get_full_path()
                    messages.info(request, 'Please confirm your password to continue.')
                    return redirect('accounts_app:confirm_password')
            else:
                # No recent authentication recorded
                request.session['next_after_reauth'] = request.get_full_path()
                messages.info(request, 'Please confirm your password to continue.')
                return redirect('accounts_app:confirm_password')
            
            return view_func(request, *args, **kwargs)
        
        return wrapped_view
    return decorator


class SecurityMixin:
    """
    Mixin for class-based views to add comprehensive security.
    
    Usage:
        class MyView(SecurityMixin, View):
            security_check_account = True
            security_validate_input = True
            security_require_https = True
    """
    
    security_check_account = False
    security_validate_input = False
    security_require_https = False
    security_log_access = False
    security_require_recent_auth = False
    security_recent_auth_max_age = 1800  # 30 minutes
    
    @method_decorator(never_cache)
    def dispatch(self, request, *args, **kwargs):
        # Require HTTPS if configured
        if self.security_require_https and not request.is_secure() and not settings.DEBUG:
            secure_url = request.build_absolute_uri().replace('http://', 'https://')
            return redirect(secure_url)
        
        # Check account security if configured
        if self.security_check_account and request.user.is_authenticated:
            client_ip = get_client_ip(request)
            lockout_result = check_account_lockout(request.user, client_ip)
            if lockout_result['is_locked']:
                messages.error(request, 'Your account is temporarily locked.')
                return redirect('accounts_app:rate_limit_exceeded')
        
        # Validate input security if configured
        if self.security_validate_input and request.method in ['POST', 'PUT', 'PATCH']:
            for key, value in request.POST.items():
                if isinstance(value, str):
                    if contains_xss_patterns(value) or contains_sql_patterns(value):
                        messages.error(request, 'Your request contains suspicious content.')
                        return redirect(request.path)
        
        # Check recent authentication if configured
        if self.security_require_recent_auth and request.user.is_authenticated:
            last_auth = request.session.get('last_authentication')
            if last_auth:
                from datetime import datetime, timedelta
                last_auth_time = datetime.fromisoformat(last_auth)
                if datetime.now() - last_auth_time > timedelta(seconds=self.security_recent_auth_max_age):
                    request.session['next_after_reauth'] = request.get_full_path()
                    return redirect('accounts_app:confirm_password')
        
        # Log access if configured
        if self.security_log_access:
            log_security_event(
                f'view_access_{self.__class__.__name__}',
                user=request.user if request.user.is_authenticated else None,
                request=request,
                details={
                    'view_class': self.__class__.__name__,
                    'args': args,
                    'kwargs': kwargs,
                }
            )
        
        response = super().dispatch(request, *args, **kwargs)
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        
        return response


# Convenience decorators combining multiple security features
def secure_view(require_https=True, check_account=True, validate_input=True, log_access=True):
    """
    Comprehensive security decorator combining multiple security checks.
    
    Usage:
        @secure_view(require_https=True, check_account=True)
        def sensitive_view(request):
            pass
    """
    def decorator(view_func):
        decorated_view = view_func
        
        if log_access:
            decorated_view = log_sensitive_access()(decorated_view)
        
        if validate_input:
            decorated_view = validate_input_security()(decorated_view)
        
        if check_account:
            decorated_view = check_account_security(decorated_view)
        
        if require_https:
            decorated_view = require_secure_connection(decorated_view)
        
        decorated_view = security_headers(decorated_view)
        
        return decorated_view
    
    return decorator


def admin_secure_view(view_func):
    """
    Security decorator specifically for admin views.
    
    Usage:
        @admin_secure_view
        def admin_view(request):
            pass
    """
    @require_secure_connection
    @check_account_security
    @validate_input_security()
    @log_sensitive_access('admin_access', 'high')
    @security_headers
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        if not request.user.is_staff:
            raise PermissionDenied("Admin access required.")
        
        return view_func(request, *args, **kwargs)
    
    return wrapped_view
