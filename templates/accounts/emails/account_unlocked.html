<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Unlocked - <PERSON>zyWish</title>
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            color: #42241A;
            font-size: 24px;
            font-weight: bold;
            font-family: 'Poppins', sans-serif;
        }
        .success-icon {
            width: 60px;
            height: 60px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            color: white;
            font-size: 24px;
        }
        .title {
            color: #2F160F;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            font-family: 'Poppins', sans-serif;
        }
        .content {
            color: #525252;
            margin-bottom: 25px;
        }
        .details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .details h4 {
            color: #2F160F;
            margin-top: 0;
            margin-bottom: 15px;
            font-family: 'Poppins', sans-serif;
        }
        .details p {
            margin: 8px 0;
        }
        .button {
            display: inline-block;
            background: #42241A;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            margin: 20px 0;
        }
        .button:hover {
            background: #2F160F;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        .security-tips {
            background: #e8f4fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .security-tips h4 {
            color: #2F160F;
            margin-top: 0;
            font-family: 'Poppins', sans-serif;
        }
        .security-tips ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .security-tips li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">CozyWish</div>
            <div class="success-icon">✓</div>
        </div>
        
        <h1 class="title">Your Account Has Been Unlocked</h1>
        
        <div class="content">
            <p>Hello {{ user.first_name|default:user.email }},</p>
            
            <p>Good news! Your CozyWish account has been successfully unlocked and you can now access your account normally.</p>
        </div>
        
        <div class="details">
            <h4>Unlock Details</h4>
            <p><strong>Unlock Reason:</strong> {{ unlock_reason|title }}</p>
            <p><strong>Unlock Time:</strong> {{ unlock_time|date:"F j, Y g:i A T" }}</p>
            <p><strong>Account Status:</strong> Active</p>
        </div>
        
        <div class="content">
            <h3>What's Next</h3>
            <p>You can now log in to your account as usual. We recommend taking the following steps to keep your account secure:</p>
            
            <ul>
                <li>Review your recent account activity</li>
                <li>Ensure your password is strong and unique</li>
                <li>Consider enabling two-factor authentication</li>
                <li>Update your security settings if needed</li>
            </ul>
        </div>
        
        <div style="text-align: center;">
            <a href="{% url 'accounts_app:login' %}" class="button">Log In to Your Account</a>
        </div>
        
        <div class="security-tips">
            <h4>🛡️ Keep Your Account Secure</h4>
            <ul>
                <li>Use a strong, unique password that you don't use elsewhere</li>
                <li>Enable two-factor authentication for extra security</li>
                <li>Log out from shared or public computers</li>
                <li>Monitor your account for any suspicious activity</li>
                <li>Keep your contact information up to date</li>
            </ul>
        </div>
        
        <div class="content">
            <p>If you experience any issues accessing your account or notice any suspicious activity, please contact our support team immediately at <a href="mailto:{{ support_email }}">{{ support_email }}</a>.</p>
            
            <p>Thank you for your patience and for helping us keep your account secure.</p>
            
            <p>Best regards,<br>
            The CozyWish Security Team</p>
        </div>
        
        <div class="footer">
            <p>This is an automated security notification from CozyWish.</p>
            <p>If you did not request this unlock, please contact support immediately.</p>
        </div>
    </div>
</body>
</html>
