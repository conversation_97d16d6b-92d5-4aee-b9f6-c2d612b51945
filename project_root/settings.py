# --- Standard Library Imports ---
import os
import sys
from pathlib import Path
import dj_database_url
from decouple import config

# --- Local App Imports ---
from config.logging import LOGGING


# --- Base Directory ---
BASE_DIR = Path(__file__).resolve().parent.parent



# --- Logs Configuration ---
LOG_LEVEL = config('LOG_LEVEL', default='INFO').upper()



# --- Core Configuration ---
SECRET_KEY = config("SECRET_KEY")
DEBUG = config("DEBUG", default=False, cast=bool)
PLATFORM_FEE_RATE = config("PLATFORM_FEE_RATE", default=0.05, cast=float)
DASHBOARD_CACHE_TIMEOUT = config("DASHBOARD_CACHE_TIMEOUT", default=300, cast=int)
NOTIFICATION_CACHE_TIMEOUT = config("NOTIFICATION_CACHE_TIMEOUT", default=60, cast=int)
ENABLE_TEST_VIEW = DEBUG



# --- Allowed Hosts & CSRF ---
ALLOWED_HOSTS = ['.cozywish.com', 'cozywish.onrender.com']
RENDER_EXTERNAL_HOSTNAME = os.environ.get('RENDER_EXTERNAL_HOSTNAME')
if RENDER_EXTERNAL_HOSTNAME:
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)
if DEBUG:
    ALLOWED_HOSTS.extend(['localhost', '127.0.0.1', 'testserver'])

CSRF_TRUSTED_ORIGINS = [
    'https://www.cozywish.com',
    'https://cozywish.onrender.com'
]




# --- Installed Applications ---
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',  # Required for allauth

    # Third-party apps
    'storages',
    'widget_tweaks',
    'crispy_forms',
    'crispy_bootstrap5',

    # Django Allauth
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',

    # Project apps
    'accounts_app',
    'utility_app',
    'venues_app',
    'discount_app',
    'booking_cart_app',
    'payments_app',
    'dashboard_app',
    'review_app',
    'notifications_app',
    'admin_app',
    'utils',
]



# --- Middleware ---
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'accounts_app.middleware.security_headers.ComprehensiveSecurityHeadersMiddleware',
    'admin_app.middleware.AdminCSPMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'accounts_app.middleware.security_enhancements.SecurityEnhancementMiddleware',
    'accounts_app.middleware.input_validation.InputValidationMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'accounts_app.middleware.security_enhancements.CSRFEnhancementMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'allauth.account.middleware.AccountMiddleware',  # Required for allauth
    'accounts_app.middleware.session_management.EnhancedSessionMiddleware',
    'accounts_app.middleware.session_management.ConcurrentSessionMiddleware',
    'accounts_app.middleware.session_management.RememberMeMiddleware',
    'accounts_app.middleware.security_enhancements.LoginAttemptTrackingMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'dashboard_app.middleware.DashboardAccessMiddleware',
]



# --- URL Configuration ---
ROOT_URLCONF = 'project_root.urls'
WSGI_APPLICATION = 'project_root.wsgi.application'



# --- Templates ---
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'admin_app.context_processors.recent_admin_links',
                'booking_cart_app.context_processors.cart_context',
                'notifications_app.context_processors.notifications_context',
                'dashboard_app.context_processors.provider_context',
            ],
        },
    },
]



# --- Database Configuration ---
DATABASE_URL = os.environ.get('DATABASE_URL')
if DATABASE_URL:
    DATABASES = {'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)}
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }


# --- Test Database Configuration ---
if 'test' in sys.argv:
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }


# --- Password Validation ---
AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]


# --- Internationalization ---
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True


# --- Testing Flag ---
TESTING = 'test' in sys.argv


# --- Static Files Configuration ---
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

if not DEBUG:
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = True


# --- Media Files Configuration ---
if DEBUG:
    MEDIA_URL = '/media/'
    MEDIA_ROOT = BASE_DIR / 'media'
    # Use default file system storage for development
    STORAGES = {
        "default": {
            "BACKEND": "django.core.files.storage.FileSystemStorage",
        },
        "staticfiles": {
            "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
        },
    }
else:
    # AWS S3 Configuration
    AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID', default=None)
    AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY', default=None)
    AWS_STORAGE_BUCKET_NAME = config('AWS_STORAGE_BUCKET_NAME', default=None)
    AWS_S3_REGION_NAME = config('AWS_S3_REGION_NAME', default='us-east-1')
    AWS_S3_CUSTOM_DOMAIN = config('AWS_S3_CUSTOM_DOMAIN', default=None)

    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY and AWS_STORAGE_BUCKET_NAME:
        # Django 4.2+ STORAGES configuration (replaces DEFAULT_FILE_STORAGE)
        STORAGES = {
            "default": {
                "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
                "OPTIONS": {
                    "access_key": AWS_ACCESS_KEY_ID,
                    "secret_key": AWS_SECRET_ACCESS_KEY,
                    "bucket_name": AWS_STORAGE_BUCKET_NAME,
                    "region_name": AWS_S3_REGION_NAME,
                    "custom_domain": AWS_S3_CUSTOM_DOMAIN,
                    "file_overwrite": False,
                    "default_acl": None,
                    "signature_version": "s3v4",
                    "addressing_style": "virtual",
                    "use_ssl": True,
                    "verify": True,
                    "object_parameters": {
                        "CacheControl": "max-age=86400",
                    },
                    "querystring_auth": True,
                    "querystring_expire": 3600,  # 1 hour
                },
            },
            "staticfiles": {
                "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
            },
        }

        # Legacy AWS settings for backward compatibility (some packages may still use these)
        # Note: DEFAULT_FILE_STORAGE is deprecated in Django 4.2+ in favor of STORAGES
        AWS_S3_FILE_OVERWRITE = False
        AWS_DEFAULT_ACL = None
        AWS_S3_VERIFY = True
        AWS_S3_USE_SSL = True
        AWS_S3_SIGNATURE_VERSION = 's3v4'
        AWS_S3_ADDRESSING_STYLE = 'virtual'
        AWS_QUERYSTRING_AUTH = True
        AWS_QUERYSTRING_EXPIRE = 3600  # 1 hour

        # Performance and caching
        AWS_S3_OBJECT_PARAMETERS = {
            'CacheControl': 'max-age=86400',
        }

        # URL configuration
        if AWS_S3_CUSTOM_DOMAIN:
            MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/'
        else:
            MEDIA_URL = f'https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com/'

        # Ensure URLs don't have double slashes
        if not MEDIA_URL.endswith('/'):
            MEDIA_URL += '/'

    else:
        # Log missing credentials for debugging
        missing_creds = []
        if not AWS_ACCESS_KEY_ID:
            missing_creds.append('AWS_ACCESS_KEY_ID')
        if not AWS_SECRET_ACCESS_KEY:
            missing_creds.append('AWS_SECRET_ACCESS_KEY')
        if not AWS_STORAGE_BUCKET_NAME:
            missing_creds.append('AWS_STORAGE_BUCKET_NAME')

        error_msg = f"AWS S3 credentials are required in production. Missing: {', '.join(missing_creds)}"
        print(f"WARNING: {error_msg}")  # Log to console instead of raising exception

        # Fallback to local storage with warning
        MEDIA_URL = '/media/'
        MEDIA_ROOT = BASE_DIR / 'media'
        STORAGES = {
            "default": {
                "BACKEND": "django.core.files.storage.FileSystemStorage",
            },
            "staticfiles": {
                "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
            },
        }


# --- Email Configuration (SendGrid) ---
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
FORCE_EMAIL_BACKEND = config('FORCE_EMAIL_BACKEND', default=False, cast=bool)

# Email backend logic:
# 1. If FORCE_EMAIL_BACKEND is True, use SMTP even in debug mode
# 2. If EMAIL_HOST_PASSWORD is set and we're not in test mode, use SMTP
# 3. Otherwise, use console backend for development
if TESTING:
    # Always use console backend for tests
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
elif FORCE_EMAIL_BACKEND or (EMAIL_HOST_PASSWORD and not DEBUG):
    # Use SMTP backend for production or when forced
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
elif EMAIL_HOST_PASSWORD and DEBUG:
    # In development with SendGrid configured, ask user what they prefer
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
else:
    # Default to console backend for development without email config
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

EMAIL_HOST = config('EMAIL_HOST', default='smtp.sendgrid.net')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='apikey')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')
SERVER_EMAIL = config('SERVER_EMAIL', default='<EMAIL>')
EMAIL_TIMEOUT = 30


# --- User Model and Primary Key ---
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
AUTH_USER_MODEL = 'accounts_app.CustomUser'


# --- Site Configuration (Required for allauth) ---
SITE_ID = 1


# --- Authentication Backends ---
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
]


# --- Authentication URLs ---
LOGIN_URL = '/accounts/customer/login/'
LOGOUT_REDIRECT_URL = '/'



# --- Cache Configuration ---
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'cozywish-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# --- Celery Configuration ---
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
if 'test' in sys.argv:
    CELERY_TASK_ALWAYS_EAGER = True
    CELERY_TASK_EAGER_PROPAGATES = True


# --- Crispy Forms Configuration ---
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"


# --- Production Security Settings ---
if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    SECURE_HSTS_SECONDS = ********
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = 'DENY'


# --- Forms URL Field Configuration ---
FORMS_URLFIELD_ASSUME_HTTPS = True


# --- Django Allauth Configuration ---
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_EMAIL_SUBJECT_PREFIX = '[CozyWish] '
ACCOUNT_DEFAULT_HTTP_PROTOCOL = 'https'
ACCOUNT_LOGOUT_REDIRECT_URL = '/'
ACCOUNT_LOGIN_REDIRECT_URL = '/dashboard/'
ACCOUNT_SIGNUP_REDIRECT_URL = '/dashboard/'

# Rate limiting configuration (replaces deprecated ACCOUNT_LOGIN_ATTEMPTS_LIMIT/TIMEOUT)
ACCOUNT_RATE_LIMITS = {
    'login_failed': '5/5m',  # 5 attempts per 5 minutes
}

# Custom Allauth Adapters for role-based user creation
ACCOUNT_ADAPTER = 'accounts_app.allauth_adapters.CozyWishAccountAdapter'
SOCIALACCOUNT_ADAPTER = 'accounts_app.allauth_adapters.CozyWishSocialAccountAdapter'

# Custom Allauth Forms
ACCOUNT_FORMS = {
    'signup': 'accounts_app.allauth_forms.CozyWishSignupForm',
    'login': 'accounts_app.allauth_forms.CozyWishLoginForm',
}

SOCIALACCOUNT_FORMS = {
    'signup': 'accounts_app.allauth_forms.CozyWishSocialSignupForm',
}

# Enhanced Email Verification Settings
EMAIL_VERIFICATION_RESEND_COOLDOWN = 300  # 5 minutes in seconds
ACCOUNT_EMAIL_CONFIRMATION_EXPIRE_DAYS = 1  # Email confirmations expire after 1 day
ACCOUNT_EMAIL_CONFIRMATION_HMAC = True  # Use HMAC for email confirmation tokens

# Enhanced Security Settings
MAX_CONCURRENT_SESSIONS = 5  # Maximum concurrent sessions per user
SESSION_TIMEOUT_WARNING_MINUTES = 5  # Minutes before session timeout to show warning

# Comprehensive Password Security Settings
PASSWORD_MIN_LENGTH = 8  # Minimum password length
PASSWORD_MAX_LENGTH = 128  # Maximum password length
PASSWORD_REQUIRE_UPPERCASE = True  # Require uppercase letters
PASSWORD_REQUIRE_LOWERCASE = True  # Require lowercase letters
PASSWORD_REQUIRE_NUMBERS = True  # Require numbers
PASSWORD_REQUIRE_SYMBOLS = True  # Require special characters
PASSWORD_HISTORY_COUNT = 5  # Number of previous passwords to remember
PASSWORD_ENABLE_BREACH_CHECK = True  # Check passwords against known breaches

# Password Strength Requirements
PASSWORD_MIN_SCORE = 60  # Minimum password strength score (0-100)
PASSWORD_REJECT_WEAK = True  # Reject passwords below minimum score
PASSWORD_WARN_MODERATE = True  # Warn about moderate strength passwords

# Breach Checking Configuration
PASSWORD_BREACH_CHECK_TIMEOUT = 5  # Timeout for breach check API calls
PASSWORD_BREACH_CHECK_CACHE_HOURS = 24  # Cache breach check results for hours

# Rate Limiting Settings
RATELIMIT_ENABLE = True  # Enable rate limiting
RATELIMIT_USE_CACHE = 'default'  # Cache backend for rate limiting

# Comprehensive Rate Limiting Configuration
RATE_LIMIT_WHITELIST = [
    '127.0.0.1',  # Localhost
    '::1',        # IPv6 localhost
]

# Rate Limit Defaults
RATE_LIMIT_DEFAULTS = {
    'login': {'limit': 5, 'window': 300},      # 5 attempts per 5 minutes
    'signup': {'limit': 3, 'window': 3600},    # 3 signups per hour
    'password_reset': {'limit': 3, 'window': 3600},  # 3 resets per hour
    'email_verification': {'limit': 3, 'window': 300},  # 3 resends per 5 minutes
    'api': {'limit': 60, 'window': 60},        # 60 requests per minute
    'general': {'limit': 30, 'window': 300},   # 30 requests per 5 minutes
}

# Progressive Rate Limiting
PROGRESSIVE_RATE_LIMITING = {
    'enabled': True,
    'base_multiplier': 2,
    'max_multiplier': 8,
    'violation_threshold': 3,  # Violations before progressive penalties kick in
}

# Account Security Settings
ACCOUNT_MAX_FAILED_ATTEMPTS = 5  # Maximum failed login attempts before lockout
ACCOUNT_LOCKOUT_DURATION = 1800  # Lockout duration in seconds (30 minutes)
ACCOUNT_PROGRESSIVE_LOCKOUT = True  # Enable progressive lockout durations

# Suspicious Activity Detection
SUSPICIOUS_IP_THRESHOLD = 10  # Failed attempts from IP before flagging as suspicious
UNUSUAL_LOCATION_THRESHOLD = 3  # Number of known locations before flagging new ones
RAPID_LOGIN_THRESHOLD = 5  # Login attempts in 5 minutes before flagging

# Security Notifications
ENABLE_SECURITY_NOTIFICATIONS = True  # Enable security notification emails
SECURITY_NOTIFICATION_EMAIL = '<EMAIL>'  # Email for security alerts

# Two-Factor Authentication
ENABLE_2FA_PREPARATION = True  # Enable 2FA setup preparation
FORCE_2FA_FOR_ADMINS = True  # Force 2FA for admin users

# Comprehensive Security Headers Configuration
SECURITY_ENABLE_HSTS = not DEBUG  # Enable HSTS in production
SECURITY_HSTS_MAX_AGE = ********  # 1 year
SECURITY_HSTS_INCLUDE_SUBDOMAINS = True  # Include subdomains in HSTS
SECURITY_HSTS_PRELOAD = True  # Enable HSTS preload

# Content Security Policy
SECURITY_ENABLE_CSP = True  # Enable Content Security Policy
SECURITY_CSP_REPORT_ONLY = DEBUG  # Use report-only mode in development
SECURITY_CSP_REPORT_URI = '/security/csp-report/'  # CSP violation reporting endpoint

# Security Headers
SECURITY_FRAME_OPTIONS = 'DENY'  # X-Frame-Options
SECURITY_CONTENT_TYPE_OPTIONS = 'nosniff'  # X-Content-Type-Options
SECURITY_XSS_PROTECTION = '1; mode=block'  # X-XSS-Protection
SECURITY_REFERRER_POLICY = 'strict-origin-when-cross-origin'  # Referrer-Policy

# External domains for CSP
CSP_EXTERNAL_DOMAINS = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
    'https://cdn.jsdelivr.net',
]

# HTTPS Configuration
SECURE_SSL_REDIRECT = not DEBUG  # Redirect HTTP to HTTPS in production
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')  # For reverse proxy
USE_TLS = not DEBUG  # Use TLS in production

# Additional Security Settings
SECURE_BROWSER_XSS_FILTER = True  # Enable XSS filtering
SECURE_CONTENT_TYPE_NOSNIFF = True  # Prevent MIME type sniffing
SECURE_HSTS_SECONDS = ******** if not DEBUG else 0  # HSTS max age
SECURE_HSTS_INCLUDE_SUBDOMAINS = not DEBUG  # HSTS subdomains
SECURE_HSTS_PRELOAD = not DEBUG  # HSTS preload

# Comprehensive Audit Logging Configuration
ENABLE_AUDIT_LOGGING = True  # Enable audit logging
ENABLE_SECURITY_LOGGING = True  # Enable security event logging
ENABLE_PERFORMANCE_LOGGING = True  # Enable performance logging

# Audit Retention Settings
AUDIT_RETENTION_DAYS = 365  # Keep audit logs for 1 year
SECURITY_RETENTION_DAYS = 730  # Keep security logs for 2 years

# Security Logging Settings
SECURITY_LOG_FAILED_LOGINS = True  # Log failed login attempts
SECURITY_LOG_SUSPICIOUS_ACTIVITY = True  # Log suspicious activities
SECURITY_ALERT_EMAIL = '<EMAIL>'  # Email for security alerts

# Input Validation Settings
MAX_REQUEST_SIZE = 10 * 1024 * 1024  # 10MB maximum request size
ENABLE_SQL_INJECTION_DETECTION = True  # Enable SQL injection detection
ENABLE_XSS_DETECTION = True  # Enable XSS detection
ENABLE_INPUT_SANITIZATION = True  # Enable input sanitization
INPUT_VALIDATION_STRICT_MODE = False  # Strict mode for input validation

# Input validation skip paths
INPUT_VALIDATION_SKIP_PATHS = [
    '/admin/',
    '/static/',
    '/media/',
    '/favicon.ico',
]

# Security Logging
SECURITY_LOG_FAILED_LOGINS = True  # Log failed login attempts
SECURITY_LOG_SUSPICIOUS_ACTIVITY = True  # Log suspicious activity
SECURITY_ALERT_EMAIL = '<EMAIL>'  # Email for security alerts

# Session Security
SESSION_COOKIE_SECURE = not DEBUG  # Use secure cookies in production
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access to session cookies
SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
CSRF_COOKIE_SECURE = not DEBUG  # Use secure CSRF cookies in production
CSRF_COOKIE_HTTPONLY = True  # Prevent JavaScript access to CSRF cookies
CSRF_COOKIE_SAMESITE = 'Lax'  # CSRF protection
CSRF_COOKIE_AGE = ********  # 1 year
CSRF_USE_SESSIONS = False  # Use cookies instead of sessions
CSRF_FAILURE_VIEW = 'accounts_app.utils.csrf_utils.csrf_failure_handler'  # Custom CSRF failure handler







"""
## Production Environment Variables
AWS_ACCESS_KEY_ID
AWS_S3_CUSTOM_DOMAIN
AWS_S3_REGION_NAME
AWS_SECRET_ACCESS_KEY
AWS_STORAGE_BUCKET_NAME
DATABASE_URL
DEBUG
EMAIL_HOST
EMAIL_HOST_PASSWORD
EMAIL_HOST_USER
EMAIL_PORT
EMAIL_USE_TLS
LOG_LEVEL
SECRET_KEY
WEB_CONCURRENCY
"""